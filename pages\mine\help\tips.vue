<template>
  <div class="wrapper">
    <!-- <u-parse :show-with-animation="true" :lazy-load="true" :selectable="true" :html="res.content" v-if="res"></u-parse> -->
    <view class="cell-group">
      <u-cell-item
       v-for="(item, index) in privacyList" :key="index"
        :title="item.title"
        @click="openDetail(item.id)"
      ></u-cell-item>
      
    </view>
  </div>
</template>
<script>
import { getArticleDetailByType,getPrivacyList } from "@/api/article";
export default {
  data() {
    return {
      res: "",
      way: {
        USER_AGREEMENT: {
          title: "服务协议",
          type: "USER_AGREEMENT",
        },
        PRIVACY_POLICY: {
          title: "隐私政策",
          type: "PRIVACY_POLICY",
        },
        LICENSE_INFORMATION: {
          title: "资质与协议",
          type: "LICENSE_INFORMATION",
        },
        ABOUT: {
          title: "关于我们",
          type: "ABOUT",
        },
        STORE_REGISTER: {
          title: "店铺入驻协议",
          type: "STORE_REGISTER",
        },
      },
      privacyList: []
    };
  },
  mounted() {},
  onLoad(option) {
    console.log(this.way)
    uni.setNavigationBarTitle({
      title: this.way[option.type].title,
    });
    this.init(option);
    this.getPrivacyList('1938479127109292034')
  },

  methods: {
    init(option) {
      getArticleDetailByType(this.way[option.type].type).then((res) => {
        if (res.data.success) {
          this.res = res.data.result;
        
        }
      });
    },
    async getPrivacyList(val){
      const res = await getPrivacyList(val);
      console.log(res);
      if (res.statusCode === 200) {
        this.privacyList = res.data.result;
      }
    },
    openDetail(type) {
      uni.navigateTo({ url: `/pages/mine/set/privacyDetail?type=${type}` });
    },
  },
};
</script>
<style lang="scss" scoped>
.wrapper {
  padding: 16rpx;
}
.cell-group {
  background: #fff;
}
</style>