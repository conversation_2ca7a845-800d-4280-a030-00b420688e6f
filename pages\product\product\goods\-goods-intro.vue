<template>
  <div>
    <view class="detail-box">
      <view class="goods-detail">
        <view class="detail_padding">
          <div class="goods-detail-box">
            <div class="goods-detail-item goods-active">商品详情</div>
          </div>
          <u-empty
            class="empty"
            text="暂无商品介绍"
            mode="data"
            v-if="!res.mobileIntro"
          ></u-empty>
          <u-parse
            class="vhtml"
            :lazy-load="true"
            :use-cache="true"
            :show-with-animation="true"
            :html="res.mobileIntro"
            :tag-style="style"
            :preview-img="false"
            @imgtap="onImgTap"
          ></u-parse>
        </view>
      </view>
      <view class="Price_description">
        <u-parse :html="Article"></u-parse>
      </view>
      
       <!-- 价格说明 -->
      <!-- <view class="Price_description">
        <view class="Price-title">
          价格说明
        </view>
        <view class="explain-content">
          <view class="explain-title">·划线价格</view>
          <view class="explain-item">
            1、该划线价可能是商品(含服务)的门市价、零售价(如品牌指导价、建议零售价)、历史标价中的一项。
          </view>
          <view class="explain-item">
            2、由于地区、时间的差异性和市场行情波动，门市价、零售价等可能会与您购买时展示的不一致，该价格仅供您参考。
          </view>
          <view class="explain-title">·未划线价格</view>
          <view class="explain-item">
            1、指商品(含服务)的实时标价，不因表述差异改变性质。
          </view>
          <view class="explain-item">
            2、最终成交价可能会因优惠、促销、让利等更低，最终的成交价格以订单结算页价格为准。
          </view>
        </view>
      </view> -->
       <!-- 售后规则 -->
      <!-- <view class="">
        <view class="explain-title">售后规则</view>
        
        <view class="explain-content">
          <view class="explain-item">温馨提示:本部分内容为鹿优选《售后规则》内容摘要，其他具体内容见《售后规则》(位置:【我的】—【设置】—【售后政策】)</view>
          <view class="explain-item">尊敬的臻小选客户您好!您为了生活消费需要通过网络购买商品，自收到商品之日起七日内依照《消费者权益保护法》第二十五条规定退货退款的，适用七日无理由退货退款。</view>
          <view class="explain-item">一、下列商品不适用七日无理由退货规定:</view>
          <view class="explain-item">(一)您定作的商品;(二)鲜活易腐的商品</view>
          <view class="explain-item">(三)在线下载或者您拆封的音像制品、计算机软件等数字化商品;(四)交付的报纸、期刊。</view>
          <view class="explain-item">二、下列性质的商品经您在购买时确认，臻小选可以不适用七日无理由退货规定:</view>
          <view class="explain-item">(一)拆封后易影响人身安全或者生命健康的商品，或者拆封后易导致商品品质发生改变的商品</view>
          <view class="explain-item">(二)经激活或者试用后价值贬损较大的商品</view>
          <view class="explain-item">(三)臻小选销售时已明确的临近保质期的商品有瑕疵的商品;(四)其他根据商品性质并经您在购买时确认不宜退货的商品，如易受价格波动、真伪难以保证的特殊商品(例如金、银、钻石、酒水等)，您在阅读本售后规则摘要或售后政规则后进一步购买该商品的行为视为确认该商品不适用七天无理由退货。</view>
          <view class="explain-item">三、您退回的商品应当完好。对超出查验和确认商品品质、功能需要而使用商品，导致商品价值贬损较大的，视为商品不完好。</view>
          <view class="explain-item">四、您如果选择无理由退货，则应当自收到商品之日起七日内向臻小选发出退货通知。</view>
        </view>
      </view> -->
    </view>
   
    
    <!-- <view class="detail-box">
      <view class="goods-detail">
        <view class="detail_padding">
          <div class="goods-detail-box">
            <div class="goods-detail-item goods-active">商品参数</div>
          </div>
          <div class="param-list" v-if="goodsParams.length == 0">
            <u-empty text="暂无商品参数" mode="list"></u-empty>
          </div>
          <div
            class="params-group"
            v-for="(group, groupIndex) in goodsParams"
            :key="groupIndex"
          >
            <view style="font-weight: bold; margin-left: 10px">{{
              group.groupName
            }}</view>
            <div class="param-list">
              <div
                class="param-item"
                v-for="(param, index) in group.goodsParamsItemDTOList"
                :key="index"
              >
                <div class="param-left">{{ param.paramName }}</div>
                <div class="param-right">{{ param.paramValue }}</div>
              </div>
            </div>
          </div>
        </view>
      </view>
    </view> -->
  </div>
</template>

<script>
import { getGoodsMessage,getGoodsArticle } from "@/api/goods";
export default {

  data() {
    return {
      goodsDetail: "",
      Article:'',
      style: {
        img:"display:block"
			}
    };
  },
  props: ["res", "goodsId", "goodsParams"],
  async mounted() {
    this.getGoodsArticle()
    let res = await getGoodsMessage(this.goodsId);
    if (res.data.success) {
      this.goodsDetail = res.data.result;
    }
  },
  methods: {
    async getGoodsArticle() {
      let res =  await getGoodsArticle('GOODS_SPEC');
      if (res.data.success) {
        console.log('123456',res);
        this.Article = res.data.result.content;
      }
     
    },
    onImgTap(e) {
      // 阻止图片预览
      e.ignore();
    }
  }
};
</script>

<style lang="scss" scoped>
@import "../product.scss";
.param-list {
  padding: 40rpx 0 80rpx 0;
}
.detail-box {
  border-radius: 0;
}
.param-item {
  display: flex;
  justify-content: center;
  border-bottom: none;

  > .param-left,
  > .param-right {
    padding: 16rpx 0;
    font-size: 24rpx;
    color: #666;
    border: 1px solid rgb(220, 223, 230);
    border-bottom: none;
  }
  > .param-left {
    text-align: center;
    border-right: none;
    flex: 3;
  }

  > .param-right {
    padding: 0 10rpx;
    align-items: center;
    display: flex;
    flex: 7;
  }
}
.param-item:nth-last-of-type(1) {
  > .param-left,
  > .param-right {
    border-bottom: 1px solid rgb(220, 223, 230);
  }
}
.Price-title {
  font-family: "PingFang SC, PingFang SC";
  font-weight: 500;
  font-size: 32rpx;
  color: #000000;
}
.empty {
  margin: 40rpx 0;
}
.price-explain-box {
  margin-top: 20rpx;
  padding: 30rpx;
  font-size: 28rpx;
  color: #333;
}
.explain-content {
  padding-top: 30rpx;
}
.explain-title {
  font-weight: bold;
  font-size: 30rpx;
  margin-bottom: 20rpx;
}
.explain-item {
  font-size: 24rpx;
  color: #666;
  line-height: 1.8;
  margin-bottom: 10rpx;
}
.goods-detail /deep/ .vhtml {
  overflow: hidden;

  width: 100%;
}
.vhtml {
  /deep/ img {
    display: block !important;
  }
}

/deep/ img {
  width: 100%;
}
.goods-detail-box {
  display: flex;
  justify-content: space-between;
  // padding: 0 80rpx;
  height: 120rpx;
  line-height: 120rpx;
  > .goods-active {
    font-weight: 500;
    &::before {
      position: absolute;
      left: 50%;
      bottom: 15px;
      -webkit-transform: translateX(-50%);
      transform: translateX(-50%);
      // content: "";
      display: block;
      width: 52rpx;
      height: 6rpx;

      background-image: linear-gradient(90deg, $price-color, $price-light-color);
    }
  }
  > .goods-detail-item {
    color: #262626;
    position: relative;
  }
}
.detail_padding {
}
.Price_description {
  line-height: 1.8;
  margin-bottom: 32rpx;
}
</style>
