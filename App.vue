<script>
	/**
	 * vuex管理登录状态，具体可以参考官方登录模板示例
	 */
	import config from "@/config/config";
import {
	getClipboardData
} from "@/js_sdk/h5-copy/h5-copy.js";
import APPUpdate from "@/plugins/APPUpdate";
import storage from "@/utils/storage";
import {
	mapMutations
} from "vuex";


	
	
	/**
	 * 路由监听并删除路由
	 * https://developers.weixin.qq.com/miniprogram/dev/api/route/wx.navigateTo.html
	 * */
	// #ifdef MP-WEIXIN
	wx.onAppRoute((res) => {
		
	})
	// #endif

	export default {
		data() {
			return {
				config,
			};
		},


		/**
		 * 监听返回
		 */
		onBackPress(e) {
			console.log("onBackPress-APP", e);
			if (e.from == "backbutton") {
				let routes = getCurrentPages();
				let curRoute = routes[routes.length - 1].options;
				routes.forEach((item) => {
					if (
						item.route == "pages/tabbar/cart/cartList" ||
						item.route.indexOf("pages/product/goods") != -1
					) {
						uni.redirectTo({
							url: item.route,
						});
					}
				});

				if (curRoute.addId) {
					uni.reLaunch({
						url: "/pages/tabbar/cart/cartList",
					});
				} else {
					uni.navigateBack();
				}
				return true; //阻止默认返回行为
			}
		},
		methods: {
			...mapMutations(["login", "setShowEnjoyCard"]),
			
			

			/**
			 * 微信小程序版本提交更新版本 解决缓存问题
			 */
			applyUpdateWeChat() {
				const updateManager = uni.getUpdateManager();

				updateManager.onCheckForUpdate(function(res) {
					// 请求完新版本信息的回调
				});

				updateManager.onUpdateReady(function(res) {
					uni.showModal({
						title: "更新提示",
						content: "发现新版本，是否重启应用？",
						success(res) {
							if (res.confirm) {
								// 新的版本已经下载好，调用 applyUpdate 应用新版本并重启
								updateManager.applyUpdate();
							}
						},
					});
				});
				updateManager.onUpdateFailed(function(res) {
					// 新的版本下载失败
				});
			},

			//  TODO 开屏广告 后续优化添加
			launch() {
				// 检查版本更新
				APPUpdate();
			},

			/**
			 * 获取粘贴板数据
			 */
			async getClipboard() {
				let res = await getClipboardData();

				/**
				 * 解析粘贴板数据
				 */

				if (res.indexOf(config.shareLink) != -1 && (res != this.$store.state.shareLink)) {
					this.$store.state.shareLink = res
					uni.showModal({
						title: "提示",
						content: "检测到一个分享链接是否跳转？",
						confirmText: "跳转",
						success: function(callback) {
							if (callback.confirm) {
								const path = res.split(config.shareLink)[1];
								if (path.indexOf("tabbar") != -1) {
									uni.switchTab({
										url: path,
									});
								} else {
									uni.navigateTo({
										url: path,
									});
								}
							}
						},
					});
				}
			},

			/**
			 * h5中打开app获取跳转app的链接并跳转
			 */
			checkArguments() {
				// #ifdef APP-PLUS
				setTimeout(() => {
					const args = plus.runtime.arguments;
					if (args) {
						const argsStr = decodeURIComponent(args);
						const path = argsStr.split("//")[1];
						if (path.indexOf("tabbar") != -1) {
							uni.switchTab({
								url: `/${path}`,
							});
						} else {
							uni.navigateTo({
								url: `/${path}`,
							});
						}
					}
				});
				// #endif
			},
		},
		onLaunch: function(val) {
			if(val.query.inviter){
				storage.setInviter(val.query.inviter)
			}

      // #ifdef APP-PLUS
			// 重点是以下： 一定要监听后台恢复 ！一定要
			plus.globalEvent.addEventListener("newintent", (e) => {
				this.checkArguments(); // 检测启动参数
			});
			
			// 启动时检查版本更新
			this.launch();
			// #endif

			// #ifdef MP-WEIXIN
			this.applyUpdateWeChat();
			// #endif

			
		},

		onShow() {
			// #ifndef H5
			if(this.config.enableGetClipboard){
				this.getClipboard();
			}
			// #endif
			// #ifdef APP-PLUS

			if (storage.getShow()) {
				if (uni.getSystemInfoSync().platform == 'ios') {
					this.$u.route("/pages/tabbar/screen/fullScreen");

				}
			}
			// #endif
		},
	};
</script>

<style lang="scss">
	@import "uview-ui/index.scss";

	// -------适配底部安全区  苹果x系列刘海屏

	// #ifdef MP-WEIXIN
	.mp-iphonex-bottom {
		padding-bottom: constant(safe-area-inset-bottom);
		padding-bottom: env(safe-area-inset-bottom);
		box-sizing: content-box;
		height: auto !important;
		padding-top: 10rpx;
	}

	// #endif

	body {
		background-color: $bg-color;
	}

	/************************ */
	.w200 {
		width: 200rpx !important;
	}

	.flex1 {
		flex: 1; //必须父级设置flex
	}

	/* 通用解决方案（全平台） */
	.uni-tabbar-border {
		background-color: transparent !important;
		height: 0 !important;
		border: none !important;
	}
	
	/* 小程序平台强化处理 */
	/* #ifdef MP-WEIXIN */
	uni-tabbar .uni-tabbar__border {
		display: none !important;
	}
	/* #endif */
	
	/* 专门处理 APP 平台 */
	/* #ifdef APP-PLUS */
	/* 移除 iOS 阴影线 */
	.uni-tabbar.uni-tabbar--topselected {
		border-top: none !important;
	}
	
	/* 移除 Android 边框线 */
	.uni-tabbar {
		border-top-color: transparent !important;
		border-top-width: 0px !important;
		box-shadow: none !important;
	}
	
	/* 处理 iOS 15+ 新样式 */
	.uni-tabbar__content {
		background-image: none !important;
	}
	/* #endif */
</style>
