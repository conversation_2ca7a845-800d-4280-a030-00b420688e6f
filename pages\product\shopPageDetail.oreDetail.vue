  * 生成带水印的图片
 */
generateWatermarkedImage() {
  const imgPath = this.licenseImage; // 原始静态资源路径
  const watermarkText = '臻小选平台资质展示专用其他无效'; // 水印文本

  uni.downloadFile({
    url: imgPath,
    success: (downloadRes) => {
      if (downloadRes.statusCode === 200) {
        const tempFilePath = downloadRes.tempFilePath;

        uni.getImageInfo({
          src: tempFilePath,
          success: (imgRes) => {
            this.canvasWidth = imgRes.width;
            this.canvasHeight = imgRes.height;

            this.$nextTick(() => {
              const ctx = uni.createCanvasContext('watermarkCanvas', this);
              ctx.drawImage(imgRes.path, 0, 0, imgRes.width, imgRes.height);

              // 设置水印样式
              ctx.setFontSize(imgRes.width * 0.08); 
              ctx.setFillStyle('rgba(0, 0, 0, 0.2)');
              ctx.setTextAlign('left');

              // 旋转Canvas，实现倾斜水印
              ctx.translate(imgRes.width / 2, imgRes.height / 2);
              ctx.rotate((-30 * Math.PI) / 180);
              ctx.translate(-imgRes.width / 2, -imgRes.height / 2);

              // 重复绘制水印
              const textWidth = ctx.measureText(watermarkText).width;
              const textHeight = imgRes.width * 0.08; 

              for (let i = -imgRes.width; i < imgRes.width * 2; i += textWidth + 100) {
                for (let j = -imgRes.height; j < imgRes.height * 2; j += textHeight + 80) {
                  ctx.fillText(watermarkText, i, j);
                }
              }

              ctx.draw(false, () => {
                uni.canvasToTempFilePath({
                  canvasId: 'watermarkCanvas',
                  x: 0,
                  y: 0,
                  width: this.canvasWidth,
                  height: this.canvasHeight,
                  destWidth: this.canvasWidth,
                  destHeight: this.canvasHeight,
                  success: (res) => {
                    this.watermarkedLicenseImage = res.tempFilePath;
                    console.log('Watermarked image generated:', this.watermarkedLicenseImage);
                  },
                  fail: (err) => {
                    console.error('Failed to generate watermarked image:', err);
                  },
                }, this);
              });
            });
          },
          fail: (err) => {
            console.error('Failed to get image info after download:', err);
          },
        });
      } else {
        console.error('Failed to download image, status code:', downloadRes.statusCode);
      }
    },
    fail: (err) => {
      console.error('Failed to download image:', err);
    },
  });
}, 