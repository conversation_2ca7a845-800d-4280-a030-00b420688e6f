<template>
  <view class="shop-page-detail">
    <view class="content">
      <view class="store-info-card">
        <view class="store-header">
          <image
            class="store-avatar"
            :src="storeInfo.storeLogo"
            mode="aspectFill"
          ></image>
          <view class="store-details">
            <view class="store-name">{{storeInfo.storeName}}</view>
            <view class="store-tags">
                <text class="tag-item fast-delivery">快速发货</text>
                <text class="tag-item fast-quality">正品保证</text>
                <text class="tag-item brand-selected">品牌精选</text>
            </view>
          </view>
        </view>
      </view>

      <view class="business-license-card">
        <view class="card-title">营业执照</view>
        <view class="license-image-wrapper">
          <view class="license-display-area">
            <image
              class="license-image"
              src="/static/zc.png"
              mode="widthFix"
            ></image>
            <view class="preview-button" @click="previewLicense">
              点击查看原图
            </view>
          </view>
        </view>
      </view>
    </view>
    <!-- 隐藏的Canvas用于生成带水印的图片 -->
    <canvas canvas-id="watermarkCanvas" :style="{ width: canvasWidth + 'px', height: canvasHeight + 'px', position: 'fixed', left: '-9999px', top: '-9999px' }"></canvas>
  </view>
</template>

<script>
import { getStoreBaseInfo,getStoreBaseLicense } from "@/api/store.js";
export default {
  data() {
    return {
      licenseImage: "/static/zc.png",
      storeId:'',
      storeInfo:{},
      canvasWidth: 0,
      canvasHeight: 0,
      watermarkedLicenseImage: '/static/zc.png',
    };
  },
  methods: {
    goBack() {
      uni.navigateBack();
    },
    previewLicense() {
      
      this.getStoreBaseLicense()
    },
    /**
     * 生成带水印的图片
     */
    generateWatermarkedImage() {
      const imgPath = this.licenseImage; // 原始静态资源路径
      const watermarkText = '臻小选平台资质展示专用其他无效'; // 水印文本
      uni.downloadFile({
        url: imgPath,
        success: (downloadRes) => {
          if (downloadRes.statusCode === 200) {
            const tempFilePath = downloadRes.tempFilePath;

            uni.getImageInfo({
              src: tempFilePath,
              success: (imgRes) => {
                this.canvasWidth = imgRes.width;
                this.canvasHeight = imgRes.height;

                this.$nextTick(() => {
                  const ctx = uni.createCanvasContext('watermarkCanvas', this);
                  ctx.drawImage(imgRes.path, 0, 0, imgRes.width, imgRes.height);

                  // 设置水印样式
                  ctx.setFontSize(imgRes.width * 0.08); 
                  ctx.setFillStyle('rgba(0, 0, 0, 0.2)');
                  ctx.setTextAlign('left');

                  // 旋转Canvas，实现倾斜水印
                  ctx.translate(imgRes.width / 2, imgRes.height / 2);
                  ctx.rotate((-30 * Math.PI) / 180);
                  ctx.translate(-imgRes.width / 2, -imgRes.height / 2);

                  // 重复绘制水印
                  const textWidth = ctx.measureText(watermarkText).width;
                  const textHeight = imgRes.width * 0.08; 

                  for (let i = -imgRes.width; i < imgRes.width * 2; i += textWidth + 100) {
                    for (let j = -imgRes.height; j < imgRes.height * 2; j += textHeight + 80) {
                      ctx.fillText(watermarkText, i, j);
                    }
                  }

                  ctx.draw(false, () => {
                    uni.canvasToTempFilePath({
                      canvasId: 'watermarkCanvas',
                      x: 0,
                      y: 0,
                      width: this.canvasWidth,
                      height: this.canvasHeight,
                      destWidth: this.canvasWidth,
                      destHeight: this.canvasHeight,
                      success: (res) => {
                        this.watermarkedLicenseImage = res.tempFilePath;
                        console.log('Watermarked image generated:', this.watermarkedLicenseImage);
                      },
                      fail: (err) => {
                        console.error('Failed to generate watermarked image:', err);
                      },
                    }, this);
                  });
                });
              },
              fail: (err) => {
                console.error('Failed to get image info after download:', err);
              },
            });
          } else {
            console.error('Failed to download image, status code:', downloadRes.statusCode);
          }
        },
        fail: (err) => {
          console.error('Failed to download image:', err);
        },
      });
    },
    /**
     * 店铺信息
     */
    async getStoreData() {
      let res = await getStoreBaseInfo(this.storeId);
      if (res.data.success) {
        this.storeInfo = res.data.result;
        
      } else {
        uni.reLaunch({
          url: "/",
        });
      }
    },
    async getStoreBaseLicense() {
      let res = await getStoreBaseLicense(this.storeId);
      if (res.data.success) {
        this.license = res.data.result.licencePhoto;
         uni.previewImage({
          urls: [this.license],
          // current: this.watermarkedLicenseImage,
        });
      }
    },
  },
  mounted() {
    // #ifdef MP-WEIXIN
    // 小程序默认分享
    uni.showShareMenu({
      withShareTicket: true,
    });
    // #endif
    this.getStoreData();
    // this.generateWatermarkedImage(); // 在页面加载后生成带水印的图片
  },
  async onLoad(options) {
    this.storeId = options.id;
    console.log(this.storeId,'this.storeId')
  },
};
</script>

<style lang="scss" scoped>
.shop-page-detail {
  background-color: #F7F7F7;
}

.content {
  padding: 20rpx 32rpx;
}

.store-info-card {
  background-color: #fff;
  border-radius: 20rpx;
  padding: 32rpx 0 32rpx 24rpx;
  margin-bottom: 20rpx;
}

.store-info-card {
  .store-header {
    display: flex;
    align-items: center;
    .store-avatar {
      width: 88rpx;
      height: 88rpx;
      border-radius: 50%;
      margin-right: 20rpx;
      background-color: #f0f0f0; // Placeholder background
    }
    .store-details {
      .store-name {
        font-weight: 400;
        font-size: 28rpx;
        color: #000000;
        margin-bottom: 14rpx;
      }
      .store-tags {
        display: flex;
        align-items: center;

        .tag-item {
            font-size: 20rpx;
            padding: 6rpx 12rpx;
            border-radius: 8rpx;
            margin-right: 10rpx;
            color: #fff;
            line-height: 1;

            &.fast-delivery {
                background: linear-gradient( 90deg, #FF5235 0%, #FF9500 100%);
                color: #fff;
            }
            &.fast-quality {
                background: #21214A;
                // color: #FFE9C0;
                color: #fff;
            }
            &.brand-selected {
                background: linear-gradient( 90deg, #AC8E6F 0%, #E6C186 100%);
                // color: #FFE9B8;
                color: #fff;
            }
        }
      }
    }
  }
}

.business-license-card {
  .card-title {
    font-family: "PingFang SC, PingFang SC";
    font-weight: 400;
    font-size: 32rpx;
    color: #000000;
    margin-bottom: 20rpx;
    position: relative;
    padding-left: 20rpx;

    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 6rpx;
      height: 40rpx;
      background: #FF5134;
      border-radius: 44rpx;
    }
  }
  .license-image-wrapper {
    text-align: center;
    .license-display-area {
      position: relative;
       
      .license-image {
        width: 100%;
        border-radius: 8rpx;
        box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
        background: #FFF;
        filter: blur(5rpx);
      }

      &::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5); // 黑色半透明
        border-radius: 8rpx;
        z-index: 5; // 确保在图片之上但在按钮之下
      }

      .preview-button {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 250rpx;
        height: 68rpx;
        line-height: 68rpx;
        background: linear-gradient(90deg, #FF5134 0%, #FF8F0C 100%);
        border-radius: 34rpx;
        font-size: 28rpx;
        color: #fff;
        text-align: center;
        z-index: 10;
      }
    }
  }
}
</style>
