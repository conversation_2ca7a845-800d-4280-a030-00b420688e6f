<template>
  <view class="content">
    <u-navbar
      :border-bottom="false"
      :is-back="true"
      :is-fixed="true"
      :custom-back="goToMine"
    >
      <u-search
        placeholder="搜索我的订单"
        :show-action="false"
        height="64"
        bg-color="#F7F7F7"
        margin="30rpx"
        @search="searchOrder"
        @clear="clearOrder"
        v-model="keyword"
        search-icon-color="#999"
      ></u-search>
    </u-navbar>
    <view class="tab-content-tabs">
      <view class="navbar">
        <view
          v-for="(item, index) in navList"
          :key="index"
          class="nav-item"
          :class="{ current: tabCurrentIndex === index }"
          @click="tabClick(index)"
          >{{ item.text }}</view
        >
      </view>
    </view>
    <swiper
      :current="tabCurrentIndex"
      class="swiper-box"
      duration="300"
      @change="changeTab"
    >
      <swiper-item
        class="tab-content"
        v-for="(tabItem, tabIndex) in navList"
        :key="tabIndex"
      >
        <scroll-view
          class="list-scroll-content"
          scroll-y
          @scrolltolower="loadData(tabIndex)"
        >
          <!-- 空白页 -->
          <u-empty
            text="暂无订单"
            mode="list"
            v-if="tabItem.loaded === true && tabItem.orderList.length === 0"
          ></u-empty>
          <!-- 订单列表 -->
          <view
            class="seller-view"
            :key="oderIndex"
            v-for="(order, oderIndex) in tabItem.orderList"
          >
            <!-- 店铺名称 -->
            <view class="seller-info u-flex u-row-between">
              <view class="seller-name wes" >
                <view class="name">当前订单状态</view>

                <!-- <view class="name" v-if="order.orderPromotionType">
									<u-tag 
									:text="renderOrderTag(order.orderPromotionType)" 
									:color="renderOrderTagColor(order.orderPromotionType)"
									/>
								</view> -->
              </view>
              <view class="order-sn">
                
                <text>{{ order.orderStatus | orderStatusList }}</text>
                <u-count-down
                  v-if="order.orderStatus === 'UNPAID'"
                  color="#FF5134"
                  separator-color="#FF5134"
                  :timestamp="timeFn(order.autoCancel)"
                ></u-count-down>
              </view>
            </view>
            <view>
              <view>
                <view
                  class="goods-item-view"
                  @click="navigateToOrderDetail(order.sn)"
                >
                  <view
                    class="goods-img"
                    v-for="(goods, goodsIndex) in order.orderItems"
                    :key="goodsIndex"
                  >
                    <u-image
                      border-radius="6"
                      width="100%"
                      height="100%"
                      :src="goods.image"
                    ></u-image>
                  </view>
                  <view class="goods-info">
                    <view
                      v-if="order.orderItems.length <= 1"
                      class="goods-title u-line-2"
                    >
                      {{ order.groupName }}</view
                    >
                    <!-- 规格  -->

                    <view class="goods-spec">
                      {{ formatSpecs(order.specs) || order.skuName || "" }}
                    </view>
                    <!-- 价格order.flowPrice | unitPrice -->
                    <view
                      v-if="order.orderItems.length <= 1"
                      class="goods-price"
                    >
                      ￥{{ order.orderItems[0].goodsPrice | unitPrice }}
                      <text v-if="order.orderItems.length <= 1"
                        >x{{ order.groupNum }}</text
                      >
                    </view>
                  </view>
                  <!-- <view v-if="order.orderItems.length <= 1" class="goods-num">
										<view>x{{ order.groupNum }}</view>
									</view> -->
                </view>
              </view>
              <view class="btn-view u-flex u-row-between">
                <view class="description">
                  <!-- 等待付款 -->
                  <view class="num_gto">共{{ order.groupNum }}件</view>
                  <div v-if="order.payStatus === 'PAID'">已付金额:</div>
                  <div v-else>应付金额:</div>
                  <div class="price">￥{{ order.flowPrice | unitPrice }}</div>
                </view>
              </view>
              <view class="goods-btn flex flex-a-c">
                <!-- 全部 -->
                <!-- <view ripple class="pay-btn" shape="circle" size="mini"
										 @click="waitPay(order)">支付</view>
									<view ripple class="pay-btn" shape="circle" size="mini"
										 @click="waitPay(order)">再次购买</view>
									<view ripple class="cancel-btn" shape="circle" size="mini"
										 @click="onCancel(order.sn)">
										取消订单
									</view>
									<view ripple shape="circle" class="cancel-btn" size="mini"
										
										@click="applyService(order)">
										售后详情
									</view>  -->
                <view
                  ripple
                  class="pay-btn"
                  shape="circle"
                  size="mini"
                  v-if="order.allowOperationVO.pay"
                  @click="waitPay(order)"
                  >立即付款</view
                >
                <!-- 取消订单  v-if="order.allowOperationVO.cancel"-->
                <view
                  ripple
                  class="cancel-btn"
                  shape="circle"
                  size="mini"
                  v-if="order.orderStatus=='UNPAID'"
                  @click="onCancel(order.sn, order.flowPrice)"
                >
                  取消订单
                </view>
                <!-- 申请售后 -->
                <view
                  ripple
                  class="cancel-btn"
                  shape="circle"
                  size="mini"
                  v-if="(order.orderStatus=='UNDELIVERED' || order.orderStatus=='DELIVERING' || order.orderStatus=='DELIVERED'|| order.orderStatus=='COMPLETED') && order.groupAfterSaleStatus != 'ALREADY_APPLIED'"
                  @click="applyService(order.sn, order,tabItem)"
                >
                  申请售后
                </view>
                <!-- 等待收货 -->
                <view
                  ripple
                  shape="circle"
                  class="rebuy-btn"
                  size="mini"
                  v-if="order.allowOperationVO.showLogistics"
                  @click="navigateToLogistics(order)"
                >
                  查看物流
                </view>
                <view
                  ripple
                  shape="circle"
                  class="pay-btn"
                  size="mini"
                  v-if="order.allowOperationVO.rog"
                  @click="onRog(order.sn)"
                >
                  确认收货
                </view>
                <!-- 评价 -->
                <view
                  ripple
                  shape="circle"
                  class="pay-btn"
                  size="mini"
                  v-if="order.groupCommentStatus=='UNFINISHED'"
                  @click="onComment(order.orderItems[0],formatSpecs(order.specs))"
                >
                  评价 
                </view>
                <!-- <view
                  ripple
                  shape="circle"
                  class="cancel-btn"
                  size="mini"
                  v-if="
                    order.groupAfterSaleStatus &&
                    (order.groupAfterSaleStatus.includes('NOT_APPLIED') ||
                      order.groupAfterSaleStatus.includes('PART_AFTER_SALE'))
                  "
                  @click="applyService(order)"
                >
                  退款/售后
                </view> -->
              </view>
            </view>
          </view>
          <uni-load-more :status="tabItem.loadStatus"></uni-load-more>
        </scroll-view>
      </swiper-item>
    </swiper>
    <u-popup
      class="cancel-popup"
      v-model="cancelShow"
      mode="bottom"
      length="60%"
    >
      <view class="header">取消订单</view>
      <view class="body">
        <view class="title"
          >温馨提示：订单一旦取消，无法恢复，金额将原路返回</view
        >
        <view class="line_name"> 请选择取消订单的原因（必选） </view>
        <view>
          <u-radio-group v-model="reason">
            <view class="value">
              <view
                class="radio-view"
                :key="index"
                v-for="(item, index) in cancelList"
              >
                <view>{{ item.reason }}</view>
                <u-radio
                  :active-color="lightColor"
                  label-size="25"
                  shape="circle"
                  :name="item.reason"
                  @change="reasonChange"
                ></u-radio>
              </view>
            </view>
          </u-radio-group>
        </view>
      </view>
      <view class="footer" v-if="reason" @click="submitCancel"> 确认 </view>
    </u-popup>
    <u-toast ref="uToast" />
    <u-modal
      :confirm-color="lightColor"
      v-model="rogShow"
      :show-cancel-button="true"
      :content="'是否确认收货?'"
      @confirm="confirmRog"
    ></u-modal>
  </view>
</template>

<script>
import uniLoadMore from "@/components/uni-load-more/uni-load-more.vue";
import * as API_trade from "@/api/trade.js"; // 引入购物车相关API
import { getOrderList, cancelOrder, confirmReceipt } from "@/api/order.js";
import { getClearReason } from "@/api/after-sale.js";
import { getGoodsList } from "@/api/goods.js";
import LiLiWXPay from "@/js_sdk/lili-pay/wx-pay.js";
export default {
  components: {
    uniLoadMore,
  },
  data() {
    return {
      keyword: "",
      lightColor: this.$lightColor,
      tabCurrentIndex: 0, //导航栏索引
      navList: [
        //导航栏list
        {
          state: 0,
          text: "全部",
          loadStatus: "more",
          orderList: [],
          pageNumber: 1,
        },
        {
          state: 1,
          text: "待付款",
          loadStatus: "more",
          orderList: [],
          pageNumber: 1,
        },
        {
          state: 2,
          text: "待发货",
          loadStatus: "more",
          orderList: [],
          pageNumber: 1,
        },
        {
          state: 3,
          text: "待收货",
          loadStatus: "more",
          orderList: [],
          pageNumber: 1,
        },
        {
          state: 4,
          text: "已完成",
          loadStatus: "more",
          orderList: [],
          pageNumber: 1,
        },
        {
          state: 5,
          text: "已取消",
          loadStatus: "more",
          orderList: [],
          pageNumber: 1,
        },
      ],
      status: "", //接收导航栏状态
      params: {
        pageNumber: 1,
        pageSize: 10,
        tag: "ALL",
      },
      orderStatus: [
        //订单状态
        {
          orderStatus: "ALL", //全部
        },
        {
          orderStatus: "WAIT_PAY", //代付款
        },
        {
          orderStatus: "WAIT_SHIP",
        },
        {
          orderStatus: "WAIT_ROG", //待收货
        },
        {
          orderStatus: "COMPLETE", //已完成
        },
        {
          orderStatus: "CANCELLED", //已取消
        },
        {
          orderStatus: "STAY_PICKED_UP", //待自提
        },
        {
          orderStatus: "DELIVERING", //发货中
        },
      ],
      cancelShow: false, //是否显示取消
      orderSn: "", //ordersn
      reason: "", //取消原因
      cancelList: "", //取消列表
      rogShow: false, //显示是否收货
      orderSearch: "",
      timestamp: 1800,
      flowPriceSn: 0,
      isSearchMode: false, // 是否处于搜索模式
      searchKeyword: "", // 当前搜索关键词
    };
  },

  /**
   * 跳转到个人中心
   */
  onBackPress(e) {
    if (e.from == "backbutton") {
      uni.switchTab({
        url: "/pages/tabbar/user/my",
      });
      return true; //阻止默认返回行为
    }
  },
  onPullDownRefresh() {
    if (this.tabCurrentIndex) {
      this.initData(this.tabCurrentIndex);
    } else {
      this.initData(0);
    }
    // this.loadData(this.status);
  },
  onShow() {
    if (this.$options.filters.tipsToLogin()) {
      // 从支付页面返回时，总是刷新当前标签页的数据
      this.initData(this.tabCurrentIndex);
    }
    // this.loadData(this.status);
  },

  onLoad(options) {
    /**
     * 修复app端点击除全部订单外的按钮进入时不加载数据的问题
     * 替换onLoad下代码即可
     */
    let status = Number(options.status);
    this.status = status;

    this.tabCurrentIndex = status;
    // if (status == 0) {
    //   this.loadData(status);
    // }
  },

  watch: {
    /**监听更改请求数据 */
    tabCurrentIndex(val) {
      this.params.tag = this.orderStatus[val].orderStatus;
      //切换标签页将所有的页数都重置为1
      this.navList.forEach((res) => {
        res.pageNumber = 1;
        res.loadStatus = "more";
        res.orderList = [];
      });
      this.loadData(val);
    },
  },
  methods: {
    clearOrder() {
      this.keyword = "";
      this.isSearchMode = false;
      this.searchKeyword = "";
      this.tabCurrentIndex = 0;
      this.initData(0);
    },
    searchOrder(value) {
      if (!value) {
        this.clearOrder();
        return;
      }
      this.isSearchMode = true;
      this.searchKeyword = value;
      // 调用订单接口进行搜索
      getOrderList({ ...this.params, keywords: value, pageNumber: 1 }).then(
        (res) => {
          if (res.data && res.data.result && res.data.result.records) {
            this.tabCurrentIndex = 0;
            this.navList[0].orderList = res.data.result.records;
            this.navList[0].loaded = true;
            // 判断是否还有更多数据
            if (res.data.result.records.length === this.params.pageSize) {
              this.navList[0].loadStatus = "more";
            } else {
              this.navList[0].loadStatus = "noMore";
            }
          } else {
            this.navList[0].orderList = [];
            this.navList[0].loaded = true;
            this.navList[0].loadStatus = "noMore";
          }
        }
      );
    },
    // 售后
    applyService(sn,order,tabItem) {
      console.log(sn,order,tabItem);
      // 判断发货状态
      if (order.orderStatus === "DELIVERING" || order.orderStatus === "UNDELIVERED") {
        // 未发货
        // 这里可以做你想做的逻辑，比如弹窗提示、跳转不同页面等
        uni.showToast({
          title: "该商品未发货",
          icon: "none",
        });
        // return; // 如果不想继续后续逻辑可以加 return
        uni.navigateTo({
          url: `/pages/order/afterSales/afterSalesDetail?sn=${order.groupOrderItemsSn}&value=${3}&flowPrice=${order.flowPrice}`,
        });
      } else if (
        order.orderStatus === "DELIVERED" ||
        order.orderStatus === "PARTS_DELIVERED" ||
        
        order.orderStatus === "COMPLETED"
      ) {
        console.log("已发货");

        // 已发货
        // 这里可以做你想做的逻辑，比如弹窗提示、跳转不同页面等
        uni.showToast({
          title: "该商品已发货",
          icon: "none",
        });
        // return; // 如果不想继续后续逻辑可以加 return
        uni.navigateTo({
          url: `/pages/order/afterSales/afterSalesDetail?sn=${order.groupOrderItemsSn}&value=${1}&flowPrice=${order.flowPrice}`,
        });
      }
      // uni.navigateTo({
      //     url: `/pages/order/afterSales/afterSalesDetail?sn=${order.groupOrderItemsSn}&value=${3}&flowPrice=${order.flowPrice}`,
      // });
     
    },

    // 店铺详情
    navigateToStore(val) {
      uni.navigateTo({
        url: "/pages/product/shopPage?id=" + val.storeId,
      });
    },
    renderOrderTag(orderPromotionType) {
      switch (orderPromotionType) {
        case "NORMAL":
          return "普通订单";
        case "PINTUAN":
          return "拼团订单";
          break;
        case "GIFT":
          return "赠品订单";
          break;
        case "POINTS":
          return "积分订单";
          break;
        case "KANJIA":
          return "砍价订单";
        default:
          return "";
      }
    },
    renderOrderTagColor(orderPromotionType) {
      switch (orderPromotionType) {
        case "NORMAL":
          return "main";
        case "PINTUAN":
          return "red";
          break;
        case "GIFT":
          return "blue";
          break;
        case "POINTS":
          return "black";
          break;
        case "KANJIA":
          return "pink";
        default:
          return "";
      }
    },
    /**
     * 取消订单
     */
    onCancel(sn, flowPrice) {
      this.flowPriceSn = flowPrice;
      this.orderSn = sn;
      this.cancelShow = true;
      // uni.showLoading({
      //   title: "加载中",
      // });
      getClearReason().then((res) => {
        if (res.data.result.length >= 1) {
          this.cancelList = res.data.result;
        }
        // if (this.$store.state.isShowToast) {
        //   uni.hideLoading();
        // }
      });
    },

    /**
     * 初始化数据
     */
    initData(index) {
      this.navList[index].pageNumber = 1;
      this.navList[index].loadStatus = "more";
      this.navList[index].orderList = [];
      this.loadData(index);
    },

    /**
     * 等待支付
     */
    waitPay(val) {
      console.log(val);

      this.$u.debounce(this.pay(val), 3000);
    },

    /**
     * 支付
     */
    pay(val) {
      if (val.sn) {
        // #ifdef MP-WEIXIN
        new LiLiWXPay({
          sn: val.sn,
          price: val.flowPrice,
          orderType: "ORDER",
        }).pay();
        // #endif
        // #ifndef MP-WEIXIN
        uni.navigateTo({
          url: "/pages/cart/payment/payOrder?order_sn=" + val.sn,
        });
        // #endif
      }
    },

    /**
     * 获取订单列表
     */
    loadData(index) {
      this.params.pageNumber = this.navList[index].pageNumber;
      let params = { ...this.params };
      // 如果是搜索模式，所有tab都带上关键词
      if (this.isSearchMode) {
        params.keywords = this.searchKeyword;
      } else {
        delete params.keywords;
      }
      getOrderList(params).then((res) => {
        uni.stopPullDownRefresh();
        if (!res.data.success) {
          this.navList[index].loadStatus = "noMore";
          return false;
        }
        let orderList = res.data.result.records;
        if (orderList.length == 0) {
          this.navList[index].loadStatus = "noMore";
        } else if (orderList.length < this.params.pageSize) {
          this.navList[index].loadStatus = "noMore";
        }
        if (orderList.length > 0) {
          this.navList[index].orderList =
            this.navList[index].orderList.concat(orderList);
          this.navList[index].pageNumber += 1;
        }
      });
    },
    //swiper 切换监听
    changeTab(e) {
      this.tabCurrentIndex = e.target.current;
    },
    //顶部tab点击
    tabClick(index) {
      this.tabCurrentIndex = index;
    },
    //删除订单
    deleteOrder(index) {
      uni.showLoading({
        title: "请稍后",
      });
      setTimeout(() => {
        this.navList[this.tabCurrentIndex].orderList.splice(index, 1);
        if (this.$store.state.isShowToast) {
          uni.hideLoading();
        }
      }, 600);
    },
    //取消订单
    cancelOrder(item) {
      uni.showLoading({
        title: "请稍后",
      });
      setTimeout(() => {
        let { stateTip, stateTipColor } = this.orderStateExp(9);
        item = Object.assign(item, {
          state: 9,
          stateTip,
          stateTipColor,
        });

        //取消订单后删除待付款中该项
        let list = this.navList[1].orderList;
        let index = list.findIndex((val) => val.id === item.id);
        index !== -1 && list.splice(index, 1);
        if (this.$store.state.isShowToast) {
          uni.hideLoading();
        }
      }, 600);
    },

    //订单状态文字和颜色
    orderStateExp(state) {
      let stateTip = "",
        stateTipColor = this.$lightColor;
      switch (+state) {
        case 1:
          stateTip = "待付款";
          break;
        case 2:
          stateTip = "待发货";
          break;
        case 9:
          stateTip = "订单已关闭";
          stateTipColor = "#909399";
          break;

        //更多自定义
      }
      return {
        stateTip,
        stateTipColor,
      };
    },
    timeFn(val){
      return (val - new Date().getTime()) / 1000;
    },

    /**
     * 跳转到订单详情
     */
    navigateToOrderDetail(sn) {
      uni.navigateTo({
        url: "./orderDetail?sn=" + sn,
      });
    },

    /**
     * 选择取消原因
     */
    reasonChange(reason) {
      this.reason = reason;
    },

    /**
     * 提交取消订单（未付款）
     */
    submitCancel() {
      cancelOrder(this.orderSn, {
        reason: this.reason,
      }).then((res) => {
        if (res.data.success) {
          uni.showToast({
            title: "订单已取消",
            duration: 2000,
            icon: "none",
          });
          this.initData(this.tabCurrentIndex);

          this.cancelShow = false;
        } else {
          uni.showToast({
            title: res.data.message,
            duration: 2000,
            icon: "none",
          });
          this.cancelShow = false;
        }
      });
    },

    /**
     * 确认收货显示
     */
    onRog(sn) {
      this.orderSn = sn;
      this.rogShow = true;
    },

    /**
     * 点击确认收货
     */
    confirmRog() {
      confirmReceipt(this.orderSn).then((res) => {
        if (res.data.code == 200) {
          uni.showToast({
            title: "已确认收货",
            duration: 2000,
            icon: "none",
          });
          this.initData(this.tabCurrentIndex);
          this.rogShow = false;
        }
      });
    },

    /**
     * 评价商品
     */
    onComment(sku,val) {
      console.log("sku", val);
      uni.navigateTo({
        url: `/pages/order/evaluate/releaseEvaluate?sn=${sku.sn}&val=${val}&sku=${encodeURIComponent(
          JSON.stringify(sku)
        )}`,
      });
    },

    /**
     * 重新购买
     */
    reBuy(order) {
      console.log(order);
      return;
      uni.navigateTo({
        url:
          "/pages/product/goods?id=" + order.id + "&goodsId=" + order.goodsId,
      });
    },

    /**
     * 查看物流
     */
    navigateToLogistics(order) {
      uni.navigateTo({
        url: "/pages/order/deliverDetail?order_sn=" + order.sn,
      });
    },

    onOrderSearch(val) {
      console.log("搜索订单：", val);
      // 这里写你的搜索逻辑，比如过滤订单或请求接口
      // val 就是输入的内容
      // 例如：this.loadDataWithSearch(val);
    },

    goToMine() {
      console.log("goToMine");
      uni.switchTab({
        url: "/pages/tabbar/user/my",
      });
    },

    formatSpecs(specs) {
      if (!specs) return "无规格";
      // 如果是字符串，尝试解析
      if (typeof specs === "string") {
        try {
          specs = JSON.parse(specs);
        } catch (e) {
          return "无规格";
        }
      }
      if (typeof specs !== "object") return "无规格";
      const arr = Object.keys(specs)
        .filter(
          (key) =>
            key !== "images" &&
            specs[key] !== undefined &&
            specs[key] !== null &&
            specs[key] !== ""
        )
        .map((key) => `${key}: ${specs[key]}`);
      return arr.length ? arr.join("；") : "无规格";
    },
    
  },
};
</script>

<style lang="scss" scoped>
page,
.content {
  background: $page-color-base;
  height: 100%;
}

.swiper-box {
  height: calc(100vh - 40px);
  // #ifdef H5
  height: calc(100vh - 40px - 44px);
  // #endif
  padding: 20rpx 32rpx 0 32rpx;
  margin-top: 80rpx;
}

.list-scroll-content {
  height: 100%;
}
.tab-content-tabs {
  position: fixed;
  z-index: 999;
  width: 100%;
}
.navbar {
  display: flex;
  height: 84rpx;
  background: #fff;
  position: relative;
  z-index: 10;
  padding: 6rpx 0;
  .nav-item {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    font-size: 28rpx;
    color: #666666;
    position: relative;
    font-weight: 400;

    &.current {
      color: #ff5134;
      &:after {
        content: "";
        position: absolute;
        left: 50%;
        bottom: 0;
        transform: translateX(-50%);
        width: 40rpx;
        height: 8rpx;
        background: #ff5134;
        border-radius: 56rpx;
      }
    }
  }
}

.uni-swiper-item {
  height: auto;
}

.seller-view {
  border-radius: 20rpx;
  background: #fff;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 24rpx 0 rgba(0, 0, 0, 0.06);
  padding: 32rpx;

  .seller-info {
    // height: 70rpx;
    // padding: 0 20rpx;
    padding-bottom: 32rpx;
    border-bottom: 1rpx solid #f5f5f5;
    display: flex;
    justify-content: space-between;
    .seller-name {
      display: flex;
      flex-direction: row;
      text-align: center;
      .name {
        // margin-left: 15rpx;
        font-weight: 400;
        font-size: 24rpx;
        color: #333333;
      }
    }

    .order-sn {
      text {
        margin-right: 12rpx;
      }
      text-align: center;
      color: #ff5134;
      font-size: 28rpx;
      display: flex;
    }
  }

  .goods-item-view {
    display: flex;
    flex-wrap: wrap;
    flex-direction: row;
    padding: 32rpx 0;
    border-bottom: 1rpx solid #f5f5f5;

    .goods-img {
      width: 148rpx;
      height: 148rpx;
      margin-right: 10rpx;
      margin-bottom: 8rpx;
      border-radius: 12rpx;
      overflow: hidden;
    }
    .goods-spec {
      font-weight: 400;
      font-size: 24rpx;
      color: #999999;
      margin-bottom: 22rpx;
    }
    .goods-info {
      padding-left: 10rpx;
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      .goods-title {
        // margin-bottom: 10rpx;
        font-weight: 400;
        font-size: 32rpx;
        color: #333333;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        width: 440rpx;
        display: block;
       
      }

      .goods-specs {
        font-size: 24rpx;
        margin-bottom: 10rpx;
        color: #cccccc;
      }

      .goods-price {
        font-weight: 500;
        font-size: 28rpx;
        color: #333333;
        text {
          font-weight: 400;
          font-size: 20rpx;
          color: #999999;
          margin-left: 8rpx;
        }
      }
    }

    .goods-num {
      width: 60rpx;
      color: $main-color;
    }
  }

  .btn-view {
    padding-top: 32rpx;
    font-size: 26rpx;

    .description {
      display: flex;
      color: #333333;
      size: 28rpx;
      flex: 1;
      justify-content: flex-end;
      .num_gto {
        margin-right: 8rpx;
      }
      .price {
        color: #ff5a00;
        font-size: 28rpx;
        font-weight: bold;
      }
    }
  }
}
/deep/ .u-drawer-bottom {
  border-top-left-radius: 40rpx;
  border-top-right-radius: 40rpx;
}
.cancel-popup {
  .header {
    display: flex;
    flex-direction: row;
    justify-content: center;
    margin: 40rpx 0rpx 20rpx;
    font-weight: 500;
    font-size: 32rpx;
    color: #333333;
  }

  .body {
    padding: 0 2rpx 32rpx 32rpx;

    .title {
      width: 686rpx;
      height: 76rpx;
      background: #f7f7f7;
      border-radius: 12rpx;
      font-weight: 400;
      font-size: 24rpx;
      color: #666666;
      text-align: center;
      line-height: 76rpx;
    }
    .line_name {
      font-weight: 500;
      font-size: 28rpx;
      color: #333333;
      margin-top: 40rpx;
    }
    /deep/ .u-radio-group {
      width: 100%;
    }
    .value {
      display: flex;
      flex-direction: column;
      margin: 20rpx 0;
      width: 100%;
      .radio-view {
        display: flex;
        width: 100%;
        align-items: center;
        justify-content: space-between;
        margin: 20rpx 0rpx;
      }
    }
  }

  .footer {
    text-align: center;
    position: absolute;
    bottom: 50rpx;
    left: 50%;
    transform: translate(-50%, 0);
    width: 670rpx;
    height: 100rpx;
    background: linear-gradient(90deg, #ff5235 0%, #ff9500 100%);
    border-radius: 86rpx;
    font-weight: 400;
    font-size: 32rpx;
    color: #ffffff;
    text-align: center;
    line-height: 100rpx;
  }
}

.goods-btn {
  display: flex;
  justify-content: flex-end;
  height: 100%;
  // margin-top: 20rpx;
}

.cancel-btn,
.pay-btn,
.rebuy-btn {
  text-align: center;
  margin-left: 16rpx;
  font-size: 28rpx;
  border-radius: 44rpx;
  line-height: 52rpx;
  width: 134rpx;
  height: 52rpx;
  font-weight: 500;
  font-size: 24rpx;
  margin-top: 20rpx;
}

.cancel-btn {
  background: #fff !important;
  color: #666666 !important;
  border: 1rpx solid rgba(0, 0, 0, 0.1);
  font-weight: normal !important;
  line-height: 52rpx !important;
  margin-top: 20rpx;
}

.pay-btn {
  background: linear-gradient( 90deg, #FF5235 0%, #FF9500 100%);
  // background: linear-gradient(90deg, #ff9900 0%, #ff5a00 100%);
  color: #fff !important;
}

.rebuy-btn {
  background: #f5f5f5 !important;
  color: #333 !important;
}

.order-navbar-search {
  width: 100vw;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0 16rpx;
  box-sizing: border-box;
}
</style>
