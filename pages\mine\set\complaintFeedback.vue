<template>
  <view class="container">
    <view class="group">
      <view class="item" @click="goTo('complaint')">
        <image class="icon" src="/static/img/complaint.png" mode="aspectFit"></image>
        <text class="text">投诉绿色通道</text>
      </view>
      <view class="item" @click="goTo('feedback')">
        <image class="icon" src="/static/img/feedback.png" mode="aspectFit"></image>
        <text class="text">意见反馈</text>
      </view>
    </view>
    <u-modal v-model="show" title="投诉服务热线" confirm-text="我知道了" confirm-color="#FF5134">
        <view class="slot-content">
            <view class="phone-row" @click="callPhone">
                <u-icon name="phone-fill"></u-icon>
                <text class="phone-number">************</text>
            </view>
            <view class="text">（工作日9:00-18：00）</view>
        </view>
    </u-modal>
  </view>
</template>

<script>
export default {
    data() {
        return {
        show: false,
        
    }
  },
  methods: {
    goTo(type) {
      if (type === 'complaint') {
        this.show = true;
      } else if (type === 'feedback') {
        uni.navigateTo({ url: '/pages/mine/set/feedBack' });
      }
    },
    callPhone() {
      // #ifdef APP
      uni.makePhoneCall({ phoneNumber: '4006163631' });
      // #endif
     
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  background: #fafafa;
}
.group {
  margin: 30rpx 0 0 0;
}
.slot-content {
    margin: 40rpx auto;
    text-align: center;
    .text {
        font-weight: 400;
        font-size: 24rpx;
        color: #999999;
        margin-top: 12rpx;
    }
    
}
.item {
  display: flex;
  align-items: center;
  background: #fff;
  border-radius: 16rpx;
  height: 104rpx;
  margin: 0 20rpx 20rpx 20rpx;
  padding: 34rpx 0 34rpx 32rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.03);
}
.icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 20rpx;
}
.text {
    font-weight: 500;
    font-size: 28rpx;
    color: #000000;
}
/deep/ .u-model__title {
  margin-top: 40rpx;
  padding: 0;
}
</style>
