<template>
  <view class="login">
    <div class="bg-image"></div>
    <div class="wrapper">
      <div v-if="!wechatLogin">
        <u-navbar :is-back="showBack" :border-bottom="false" :background="background"></u-navbar>

        <div v-if="loginMode === 'verificationCode'">
          <div class="title">欢迎登录臻小选</div>

          <u-input
            :custom-style="inputStyle"
            :placeholder-style="placeholderStyle"
            placeholder="请输入手机号"
            class="mobile-input-field"
            focus
            v-model="mobile"
            type="number"
            :maxlength="11"
          />

          <div class="code-input-wrapper" style="padding-bottom: 0">
            <!-- <verifyCode
            type="bottom"
            @confirm="submit"
            boxActiveColor="#D8D8D8"
            v-model="code"
            isFocus
            boxNormalColor="#D8D8D8"
            cursorColor="#D8D8D8"
            :maxlength="6"
          /> -->
            <u-input
              :custom-style="inputStyle2"
              :placeholder-style="placeholderStyle"
              placeholder="请输入验证码"
              v-model="code"
              :maxlength="6"
            >
            </u-input>
            <div class="fetch-code-text">
              <!-- 根据倒计时状态显示不同样式的文本 -->
              <template v-if="$refs.uCode && !$refs.uCode.canGetCode">
                <span :style="{ color: '#FF5134' }">{{ countdownNum }}</span>
                <span :style="{ color: '#999999' }"
                  >{{ countdownUnit }}{{ countdownSuffix }}</span
                >
              </template>
              <template v-else>
                <span @tap="fetchCode" :style="{ color: '#FF5134' }"
                  >获取验证码</span
                >
              </template>

              <!-- u-verification-code for logic only, hidden from view -->
              <u-verification-code
                change-text="x秒后重新获取"
                end-text="重新获取验证码"
                unique-key="page-login"
                :seconds="seconds"
                @end="end"
                @start="start"
                ref="uCode"
                @change="codeChange"
                style="display: none"
              >
              </u-verification-code>
            </div>
          </div>

          <div
            :class="!canRegisterLogin ? 'disable' : 'fetch-gradient'"
            @click="submit"
            class="btn register-login-btn"
          >
            注册/登陆
          </div>

          <div class="other-login-options">
            <!-- 判断在h5不显示本地一键登录 -->

            <span id="h5_show" class="option-text" @tap="oneClick"
              >本机号码一键登录</span
            >
            <span class="option-text" @click="loginMode = 'password'"
              >密码登录</span
            >
          </div>
        </div>

        <!-- One-Click Login (previous mode, but now explicitly switchable) -->

        <!-- Password Login (previous mode, but now explicitly switchable) -->
        <div v-else-if="loginMode === 'password'">
          <div class="title">欢迎登录臻小选</div>

          <u-input
            :custom-style="inputStyle"
            :placeholder-style="placeholderStyle"
            placeholder="请输入用户名"
            class="mobile-input-field"
            focus
            v-model="userData.username"
          />
          <div class="input-with-eye">
            <u-input
              :custom-style="inputStyle2"
              :placeholder-style="placeholderStyle"
              placeholder="请输入密码"
              class="mobile-input-field0"
              focus
              v-model="userData.password"
              type="password"
            />
          </div>

          <div class="forgot-password" @click="goToForgotPassword">忘记密码</div>

          <div
            :class="!canRegisterLogin ? 'disable' : 'fetch-gradient'"
            @click="passwordLogin"
            class="btn register-login-btn"
          >
            登录
          </div>

          <div class="other-login-options">
            <span class="option-text" @click="loginMode = 'verificationCode'"
              >验证码登录</span
            >
            <span id="h5_show" class="option-text" @tap="oneClick"
              >本机号码一键登录</span
            >
          </div>
        </div>

        <!-- Common Privacy Terms (moved outside specific login modes) -->
        <div class="flex privacy-terms-wrapper">
          <u-checkbox-group :icon-size="24" width="40rpx">
            <u-checkbox
              shape="circle"
              v-model="enablePrivacy"
              active-color="#FF5E00"
              :icon-size="24"
            ></u-checkbox>
          </u-checkbox-group>
          <div class="tips">
            我已阅读并同意<span @click="navigateToPrivacy('USER_AGREEMENT','1938481696628002817')"
              >《用户服务协议》</span
            ><span @click="navigateToPrivacy('PRIVACY_POLICY','1938440934217469954')"
              >《隐私协议》</span
            >并使用本机号码登录
          </div>

        </div>

        <!-- Common Verification Component (if needed for password/other flows) -->
        <myVerification
          v-if="codeFlag"
          @send="verification"
          class="verification"
          ref="verification"
          business="LOGIN"
        />
      </div>
      <view v-else>
        <wechatH5Login />
      </view>
    </div>
  </view>
</template>

<script>
import { openIdLogin, loginCallback } from "@/api/connect.js";
import api from "@/config/api.js";
import { sendMobile, smsLogin, userLogin,uniLogin } from "@/api/login";
import myVerification from "@/components/verification/verification.vue"; //验证码模块
import uuid from "@/utils/uuid.modified.js"; // uuid
import verifyCode from "@/components/verify-code/verify-code";
import { getUserInfo } from "@/api/members";
import { whetherNavigate } from "@/utils/Foundation"; //登录跳转
import storage from "@/utils/storage.js"; //缓存
import wechatH5Login from "./wechatH5Login.vue";
import { webConnect } from "@/api/connect.js";
import { md5 } from "@/utils/md5.js";
import UInput from "../../uview-ui/components/u-input/u-input.vue";

export default {
  components: {
    myVerification,
    verifyCode,
    wechatH5Login,
    UInput,
  },

  data() {
    return {
      uuid,
      wechatLogin: false, //是否加载微信公众号登录
      flage: false, //是否验证码验证
      codeFlag: true, //验证开关，用于是否展示验证码
      countdownNum: "", // 倒计时数字
      countdownUnit: "秒后", // 倒计时单位
      countdownSuffix: "重新获取", // 倒计时后缀文本
      loginMode: "verificationCode", // 新增：管理登录模式：'oneClick', 'verificationCode', 'password'
      enableUserPwdBox: false, //帐号密码登录
      codeColor: "#FF5134", // 初始颜色为 #FF5134
      seconds: 60, //默认验证码等待时间
      userData: {
        username: "",
        password: "",
      },
      showBack: true, // 始终显示返回按钮
      enableFetchCode: false,
      enableUserBtnColor: false,
      enablePrivacy: false, //隐私政策
      mobile: "", //手机号
      code: "", //验证码
      inputStyle: {
        height: "100rpx",
        "border-bottom": "1rpx solid #EEEEEE",
        "letter-spacing": "1rpx",
        "font-size": "32rpx",
        "line-height": "40rpx",
        color: "#333",
        padding: "0 20rpx",
      },
      inputStyle2: {
        height: "100rpx",
        "letter-spacing": "1rpx",
        "font-size": "32rpx",
        "line-height": "40rpx",
        color: "#333",
        padding: "0 20rpx",
      },
      placeholderStyle: "font-size: 32rpx;line-height: 32rpx;color: rgba(153,153,153,0.55);",
      loginList: [
        //第三方登录集合
        {
          icon: "weixin-fill",
          color: "#00a327",
          title: "微信",
          code: "WECHAT",
        },
        {
          icon: "qq-fill",
          color: "#38ace9",
          title: "QQ",
          code: "QQ",
        },
        {
          icon: "apple-fill",
          color: "#000000",
          title: "Apple",
          code: "APPLE",
        },
      ],
	    background: {
        //   backgroundColor: "#001f3f",

          // 导航栏背景图
          // background: 'url(https://cdn.uviewui.com/uview/xxx.jpg) no-repeat',
          // 还可以设置背景图size属性
          // backgroundSize: 'cover',

          // 渐变色
          // backgroundImage: 'linear-gradient(60deg, #FFFFFF 0%, #FFF4ED 100%)'
      },
      univerifyReady: false, // 是否可以一键登录
      univerifyError: null,  // 预登录失败信息
    };
  },
  onShow() {
    // 只要是app登录的全部清除内容
    // #ifdef APP-PLUS
    storage.setAccessToken("");
    storage.setRefreshToken("");
    storage.setUserInfo({});
    // #endif

    //#ifdef H5
    let isWXBrowser = /micromessenger/i.test(navigator.userAgent);
    if (isWXBrowser) {
      webConnect("WECHAT").then((res) => {
        let data = res.data;
        if (data.success) {
          window.location = data.result;
        }
      });
    }
    //#endif
  },

  mounted() {
    // #ifndef APP-PLUS
    //判断是否微信浏览器
    var ua = window.navigator.userAgent.toLowerCase();
    if (ua.match(/MicroMessenger/i) == "micromessenger") {
      this.wechatLogin = true;
      return;
    }
    // #endif
    /**
     * 条件编译判断当前客户端类型
     */
    //#ifdef H5
    this.clientType = "H5";
    //#endif

    //#ifdef APP-PLUS
    this.clientType = "APP";
    /**如果是app 加载支持的登录方式*/
    let _this = this;
    uni.getProvider({
      service: "oauth",
      success: (result) => {
        _this.loginList = result.provider.map((value) => {
          //展示title
          let title = "";
          //系统code
          let code = "";
          //颜色
          let color = "#8b8b8b";
          //图标
          let icon = "";
          //uni 联合登录 code
          let appcode = "";
          switch (value) {
            case "weixin":
              icon = "weixin-circle-fill";
              color = "#00a327";
              title = "微信";
              code = "WECHAT";
              break;
            case "qq":
              icon = "qq-circle-fill";
              color = "#38ace9";
              title = "QQ";
              code = "QQ";
              break;
            case "apple":
              icon = "apple-fill";
              color = "#000000";
              title = "Apple";
              code = "APPLE";
              break;
          }
          return {
            title: title,
            code: code,
            color: color,
            icon: icon,
            appcode: value,
          };
        });
      },
      fail: (error) => {
        uni.showToast({
          title: "获取登录通道失败" + error,
          duration: 2000,
          icon: "none",
        });
      },
    });
    //#endif

    //特殊平台，登录方式需要过滤
    // #ifdef H5
    this.methodFilter(["QQ"]);
    // #endif

    //微信小程序，只支持微信登录
    // #ifdef MP-WEIXIN
    this.methodFilter(["WECHAT"]);
    // #endif
  },
  watch: {
    userData: {
      handler(val) {
        if (this.userData.username && this.userData.password) {
          this.enableUserBtnColor = true;
        } else {
          this.enableUserBtnColor = false;
        }
      },
      deep: true,
    },
    mobile: {
      handler(val) {
        if (val.length == 11) {
          this.enableFetchCode = true;
        } else {
          // 确保手机号不足11位时禁用按钮
          this.enableFetchCode = false;
        }
      },
    },

    async flage(val) {
      if (val) {
        if (this.$refs.uCode && this.$refs.uCode.canGetCode) {
          if (this.loginMode === "password") {
            // 根据 loginMode 判断
            this.submitUserLogin();
            return;
          } else if (this.loginMode === "verificationCode") {
            // 根据 loginMode 判断
            // 向后端请求验证码
            uni.showLoading({});
            let res = await sendMobile(this.mobile);
            if (this.$store.state.isShowToast) {
              uni.hideLoading();
            }
            // 这里此提示会被this.start()方法中的提示覆盖
            if (res.data.success) {
              // this.current = 1; // 移除
              this.$refs.uCode.start();
            } else {
              uni.showToast({
                title: res.data.message,
                duration: 2000,
                icon: "none",
              });
              this.flage = false;
              this.$refs.verification.getCode();
            }
          }
        } else {
          !(this.loginMode === "password")
            ? this.$u.toast("请倒计时结束后再发送")
            : ""; // 根据 loginMode 判断
        }
      } else {
        this.$refs.verification.hide();
      }
    },
  },
  computed: {
    canRegisterLogin() {
      if (this.loginMode === 'password') {
        return this.userData.username && this.userData.password && this.enablePrivacy;
      }
      return this.mobile.length === 11 && this.code.length === 6 && this.enablePrivacy;
    },
  },
  onLoad(options) {
    if (options && options.state) {
      this.stateLogin(options.state);
    }

    // #ifdef APP-PLUS
    uni.preLogin({
      provider: 'univerify',
      success: () => {
        console.log('预登录成功');
        this.univerifyReady = true;
        this.univerifyError = null;
        this.goLogin(); // 预登录成功后直接弹出一键登录
      },
      fail: (res) => {
        console.error('预登录失败:', res);
        this.univerifyReady = false;
        this.univerifyError = res;
        this.showLoginUI = true; // 预登录失败，显示验证码登录UI
      }
    });
    // #endif
    // 只app登录
    // #ifdef APP-PLUS
    // this.goLogin();
    // #endif
  },
  methods: {
    oneClick() {
      this.goLogin();
    },
    //手机号一键登录事件
    goLogin() {
      const _this = this; // 保存 this
	  const univerifyManager = uni.getUniverifyManager()
      // 获取设备信息
      const systemInfo = uni.getSystemInfoSync();
      // 判断是否为 iPhone 16 系列
      if (systemInfo.model && systemInfo.model.includes('iPhone16')) {
        uni.showToast({
          title: "当前机型暂不支持一键登录，请使用验证码登录",
          icon: "none",
          duration: 1000 
        });
        this.loginMode = 'verificationCode';
        setTimeout(() => {
          // 关闭一键登录弹框
          univerifyManager.close()
        }, 1000); 
        return;
      }
      uni.login({
        provider: "univerify",
        univerifyStyle: {
          // 自定义登录框样式
          //参考`univerifyStyle 数据结构`
          fullScreen: true, // 是否全屏显示，默认值： false
          title: "快速登录",
          backgroundColor: "#FFF4ED", // 授权页面背景颜色，默认值：#ffffff
          backgroundImage: "/static/loginBg.png",
          icon: {
            path: "/static/logo.png", // 自定义显示在授权框中的logo，仅支持本地图片 默认显示App logo
            width: "70px",
            height: "70px",
            marginBottom: "20px",
            "borderRadius": "10px"
          },
          phoneNum: {
            color: "#000000", // 手机号文字颜色 默认值：#000000
            fontSize: "18", // 手机号字体大小 默认值：18
          },
          slogan: {
            color: "#8a8b90", //  slogan 字体颜色 默认值：#8a8b90
            fontSize: "12", // slogan 字体大小 默认值：12
          },
          // 一键登录
          authButton: {
            normalColor: "#FF5134", // 授权按钮正常状态背景颜色 默认值：#3479f5
            highlightColor: "#2861c5", // 授权按钮按下状态背景颜色 默认值：#2861c5（仅ios支持）
            disabledColor: "#73aaf5", // 授权按钮不可点击时背景颜色 默认值：#73aaf5（仅ios支持）
            textColor: "#ffffff", // 授权按钮文字颜色 默认值：#ffffff
            title: "本机号码一键登录", // 授权按钮文案 默认值："本机号码一键登录"
            marginTop: "300px",
            "backgroundColor": "#ffffff", 
          },
          // 其他登录方式
          otherLoginButton: {
            visible: true, // 是否显示其他登录按钮，默认值：true
            normalColor: "", // 其他登录按钮正常状态背景颜色 默认值：透明
            highlightColor: "", // 其他登录按钮按下状态背景颜色 默认值：透明
            textColor: "#656565", // 其他登录按钮文字颜色 默认值：#656565
            title: "其他登录方式", // 其他登录方式按钮文字 默认值："其他登录方式"
            borderColor: "", //边框颜色 默认值：透明（仅iOS支持）
            borderRadius: "0px", // 其他登录按钮圆角 默认值："24px" （按钮高度的一半）
          },
          // 自定义按钮登录方式
          buttons: {
            // 仅全屏模式生效，配置页面下方按钮  （3.1.14+ 版本支持）
            iconWidth: "45px", // 图标宽度（高度等比例缩放） 默认值：45px
            list: [
              {
                provider: "apple",
                iconPath: "/static/test.jpg", // 图标路径仅支持本地图片
              },
              {
                provider: "weixin",
                iconPath: "/static/test.jpg",
              },
            ],
          },
          privacyTerms: {
            defaultCheckBoxState: "true", // 条款勾选框初始状态 默认值： true
            textColor: "#8a8b90", // 文字颜色 默认值：#8a8b90
            termsColor: "#FF5134", //  协议文字颜色 默认值： #1d4788
            prefix: "我已阅读并同意", // 条款前的文案 默认值："我已阅读并同意"
            suffix: "并使用本机号码登录", // 条款后的文案 默认值："并使用本机号码登录"
            fontSize: "12", // 字体大小 默认值：12,
            privacyItems: [
              // 自定义协议条款，最大支持2个，需要同时设置url和title. 否则不生效
              {
                url: "https://", // 点击跳转的协议详情页面
                // title: "用户服务协议", // 协议名称
              },
            ],
          },
        },
        success(res) {
          // 登录成功
          console.log(res);
          let openid = res.authResult.openid; //拿到openid
          let access_token = res.authResult.access_token; //拿到access_token
          console.log(openid);
          console.log(access_token);
          uniLogin({ uniOpenid: openid, uniToken: access_token }).then(res => { 
            console.log(res);
            if (res.data.success) {
                    // 存储token
                    storage.setAccessToken(res.data.result.accessToken);
                    storage.setRefreshToken(res.data.result.refreshToken);
                    console.log('123');
                    
                    // 获取用户信息
                    getUserInfo().then((user) => {
                      console.log(user);
                      
                      if (user.data.success) {
                        // 存储用户信息
                        storage.setUserInfo(user.data.result);
                        storage.setHasLogin(true);
                        storage.setAutoCp(0);
                        // 登录成功提示
                       
                     
                        // 关闭当前页跳转首页
                        setTimeout(() => { 
                          uni.closeAuthView()
                          if (user.data.result.mobile) {
                            whetherNavigate();
                          } else {
                            uni.switchTab({
                              url: "/pages/tabbar/home/<USER>",
                            });
                          }
                        }, 100);
                       

                        // setTimeout(() => {
                        //   uni.closeAuthView()
                        //   uni.switchTab({
                        //     url: "/pages/tabbar/home/<USER>",
                        //   });
                        // }, 100);
                      } 
                    });
                  } else {
                    uni.showToast({
                      title: res.data.message,
                      icon: "none",
                    });
                  }
          });
         
        },
        // 当用户点击自定义按钮时，会触发uni.login的fail回调[点击其他登录方式，可以跳转页面，或执行事件]
        fail(res) {
          // 登录失败
          console.log(res.code);
          if (![30002,30003,30006].includes(res.code)) {
            console.log('验证码登录',res.code);
            // 切换到验证码登录
            _this.loginMode = 'verificationCode';
            uni.showToast({
              title: "一键登录失败，请使用验证码登录",
              icon: "none",
              duration: 1000
            });
            setTimeout(() => {
              // 关闭一键登录弹框
              univerifyManager.close()
            }, 1000);
          }
          
         
        },
      });
    },
    //联合信息返回登录
    stateLogin(state) {
      loginCallback(state).then((res) => {
        let data = res.data;
        if (data.success) {
          storage.setAccessToken(data.result.accessToken);
          storage.setRefreshToken(data.result.refreshToken);
          // 登录成功
          uni.showToast({
            title: "登录成功!",
            icon: "none",
          });
          getUserInfo().then((user) => {
            if (user.data.success) {
              storage.setUserInfo(user.data.result);
              storage.setHasLogin(true);
            } else {
              setTimeout(() => {
                uni.showToast({
                  title: user.data.message,
                  icon: "none",
                });
                storage.setAccessToken("");
                storage.setRefreshToken("");
                uni.switchTab({
                  url: "/pages/tabbar/user/my",
                });
              }, 500);
            }
          });
          getCurrentPages().length > 1
            ? uni.navigateBack({
                delta: getCurrentPages().length - 2,
              })
            : uni.switchTab({
                url: "/pages/tabbar/home/<USER>",
              });
        }
      });
    },
    /** 根据参数显示登录模块 */
    methodFilter(code) {
      let way = [];
      this.loginList.forEach((item) => {
        if (code.length != 0) {
          code.forEach((val) => {
            if (item.code == val) {
              way.push(item);
            }
          });
        } else {
          uni.showToast({
            title: "配置有误请联系管理员",
            duration: 2000,
            icon: "none",
          });
        }
      });
      this.loginList = way;
    },
    //非h5 获取openid
    async nonH5OpenId(item) {
      let _this = this;
      //获取各个openid
      await uni.login({
        provider: item.appcode,
        // #ifdef MP-ALIPAY
        scopes: "auth_user", //支付宝小程序需设置授权类型
        // #endif
        success: function (res) {
          uni.setStorageSync("type", item.code);
          //微信小程序意外的其它方式直接在storage中写入openid
          // #ifndef MP-WEIXIN
          uni.setStorageSync("openid", res.authResult.openid);
          res.authResult.unionId &&
            uni.setStorageSync("unionId", res.authResult.unionId);
          // #endif
        },
        fail(e) {
          uni.showToast({
            title: "第三方登录暂不可用！",
            icon: "none",
            duration: 3000,
          });
        },
        complete(e) {
          //获取用户信息
          uni.getUserInfo({
            provider: item.appcode,
            success: function (infoRes) {
              //写入用户信息
              uni.setStorageSync("nickname", infoRes.userInfo.nickName);
              uni.setStorageSync("avatar", infoRes.userInfo.avatarUrl);
              uni.setStorageSync(
                "unionId",
                infoRes.userInfo.unionId || infoRes.userInfo.unionid
              );

              // #ifdef MP-WEIXIN
              //微信小程序获取openid 需要特殊处理 如需获取openid请参考uni-id: https://uniapp.dcloud.net.cn/uniCloud/uni-id
              _this.weixinMPOpenID(res).then((res) => {
                //这里需要先行获得openid，再使用openid登录，小程序登录需要两步，所以这里特殊编译
                _this.goOpenidLogin("WECHAT_MP");
              });
              // #endif

              // #ifndef MP-WEIXIN
              _this.goOpenidLogin("APP");
              //#endif
            },
          });
        },
      });
    },
    //openid 登录
    goOpenidLogin(clientType) {
      // 获取准备好的参数，进行登录系统
      let params = {
        uuid: uni.getStorageSync("openid"), //联合登陆id
        source: uni.getStorageSync("type"), //联合登陆类型
        nickname: uni.getStorageSync("nickname"), // 昵称
        username: uni.getStorageSync("openid"), // 昵称
        avatar: uni.getStorageSync("avatar"), // 头像
        uniAccessToken: uni.getStorageSync("uni_access_token"), //第三方token
        type: this.clientType,
        token: { unionId: "", openId: uni.getStorageSync("openid") },
      };
      uni.getStorageSync("unionId")
        ? (params.token.unionId = uni.getStorageSync("unionId"))
        : delete params.token;

      openIdLogin(params, clientType).then((res) => {
        if (!res.data.success) {
          let errormessage = "第三方登录暂不可用";
          uni.showToast({
            // title: '未绑定第三方账号',
            title: errormessage,
            icon: "none",
            duration: 3000,
          });
          return;
        } else {
          storage.setAccessToken(res.data.result.accessToken);
          storage.setRefreshToken(res.data.result.refreshToken);
          // 登录成功
          uni.showToast({
            title: "第三方登录成功!",
            icon: "none",
          });
          // 执行登录
          getUserInfo().then((user) => {
            if (user.data.success) {
              /**
               * 个人信息存储到缓存userInfo中
               */
              storage.setUserInfo(user.data.result);
              storage.setHasLogin(true);
              uni.switchTab({
                url: "/pages/tabbar/home/<USER>",
              });
              /**
               * 计算出当前router路径
               * 1.如果跳转的链接为登录页面或跳转的链接为空页面。则会重新跳转到首页
               * 2.都不满足返回跳转页面
               */
              if (user.data.result.mobile) {
                whetherNavigate();
              } else {
                uni.navigateTo({
                  url: "/pages/passport/bindUserPhone",
                });
              }
            } else {
              setTimeout(() => {
                uni.showToast({
                  title: user.data.message,
                  icon: "none",
                });
                storage.setAccessToken("");
                storage.setRefreshToken("");
                uni.switchTab({
                  url: "/pages/tabbar/user/my",
                });
              }, 500);
            }
          });
        }
      });
    },
    //微信小程序获取openid
    async weixinMPOpenID(res) {
      await miniProgramLogin(res.code).then((res) => {
        uni.setStorageSync("openid", res.data);
      });
    },
    /**跳转到登录页面 */
    navigateLogin(connectLogin) {
      // #ifdef H5
      let code = connectLogin.code;
      let buyer = api.buyer;
      window.open(
        buyer + `/passport/connect/connect/login/web/` + code,
        "_self"
      );
      // #endif
      // #ifdef APP-PLUS
      this.nonH5OpenId(connectLogin);
      // #endif
    },

    // 提交
    submit() {
      if (!this.canRegisterLogin) {
        return; // 如果不满足条件，则不执行后续逻辑
      }
      /**
       * 清空当前账号信息
       */
      storage.setHasLogin(false);
      storage.setAccessToken("");
      storage.setRefreshToken("");
      storage.setUserInfo({});
      /**
       * 执行登录
       */
      smsLogin(
        {
          mobile: this.mobile,
          code: this.code,
        },
        this.clientType
      ).then((res) => {
        this.getUserInfoMethods(res);
      });
    },

    // 登录成功之后获取用户信息
    getUserInfoMethods(res) {
      if (res.data.success) {
        storage.setAccessToken(res.data.result.accessToken);
        storage.setRefreshToken(res.data.result.refreshToken);

        /**
         * 登录成功后获取个人信息
         */
        getUserInfo().then((user) => {
          if (user.data.success) {
            /**
             * 个人信息存储到缓存userInfo中
             */
            storage.setUserInfo(user.data.result);
            storage.setHasLogin(true);
            storage.setAutoCp(0);
            // 登录成功
            uni.showToast({
              title: "提示",
              content: "登录成功!",
              icon: "none",
            });

            whetherNavigate();
          } else {
            setTimeout(() => {
              uni.showToast({
                title: user.data.message,
                icon: "none",
              });
              storage.setAccessToken("");
              storage.setRefreshToken("");
              uni.switchTab({
                url: "/pages/tabbar/user/my",
              });
            }, 500);
          }
        });
      }
    },

    // 验证码验证
    verification(val) {
      this.flage = val == this.$store.state.verificationKey ? true : false;
      if (this.flage && this.loginMode === 'password') {
        this.submitUserLogin();
      }
    },
    // 跳转
    navigateToPrivacy(val,id) {
      uni.navigateTo({
          url: "/pages/mine/set/privacyDetail?type=" + id,
      });
     
    },
  

    // 点击获取验证码
    start() {
      this.$u.toast("验证码已发送");
      this.flage = false;
      this.codeFlag = false;

      this.$refs.verification.hide();
    },
    /**点击验证码*/
    codeChange(text) {
      const match = text.match(/^(\d+)(秒后)(.*)/); // 匹配数字、单位、后缀
      if (match) {
        this.countdownNum = match[1];
        this.countdownUnit = match[2];
        this.countdownSuffix = match[3];
      } else {
        // 非倒计时状态，通常是"重新获取验证码"
        this.countdownNum = "";
        this.countdownUnit = "";
        this.countdownSuffix = text; // 完整文本
      }
    },
    /** 结束验证码后执行 */
    end() {
      this.codeFlag = true;
    },

    passwordLogin() {
      if (!this.enablePrivacy) {
        uni.showToast({
          title: "请同意用户隐私",
          duration: 2000,
          icon: "none",
        });
        return false;
      }

      if (!this.userData.username) {
        uni.showToast({
          title: "请填写用户名",
          duration: 2000,
          icon: "none",
        });
        return false;
      }

      if (!this.userData.password) {
        uni.showToast({
          title: "请填写密码",
          duration: 2000,
          icon: "none",
        });
        return false;
      }

      if (!this.flage) {
        console.log("发送验证码失败123");
        
        this.$refs.verification.error(); //发送

        return false;
      }
    },

    // 提交用户登录
    async submitUserLogin() {
      const params = JSON.parse(JSON.stringify(this.userData));
      params.password = md5(params.password);
      try {
        let res = await userLogin(params, this.clientType);
        if (res.data.success) {
          this.getUserInfoMethods(res);
        } else {
          this.$refs.verification.getCode();
          this.flage = false;
        }
      } catch (error) {
        this.$refs.verification.getCode();
      }
    },

    // 发送验证码
    fetchCode() {
      if (!this.enablePrivacy) {
        uni.showToast({
          title: "请同意用户隐私",
          duration: 2000,
          icon: "none",
        });
        return false;
      }

      if (!this.$u.test.mobile(this.mobile)) {
        uni.showToast({
          title: "请填写正确手机号",
          duration: 2000,
          icon: "none",
        });
        return false;
      }
      if (this.countdownSuffix == "重新获取验证码") {
        if (!this.codeFlag) {
          let timer = setInterval(() => {
            if (this.$refs.verification) {
              this.$refs.verification.error(); //发送
            }
            clearInterval(timer);
          }, 100);
        }
        if (this.$store.state.isShowToast) {
          uni.hideLoading();
        }
      }
      if (!this.flage) {
        this.$refs.verification.error(); //发送

        return false;
      }
    },

    goToForgotPassword() {
      uni.navigateTo({
        url: '/pages/passport/forgot-password'
      });
    },
  },
};
</script>
<style>
page {
  height: 100vh;
  background: #fff;
}

.login {
  min-height: 100vh;
  position: relative;
}

.wrapper {
  padding: 0 40rpx;
  min-height: 100vh;
  position: relative;
  z-index: 999 !important;
  display: flex;
  flex-direction: column;
}
</style>
<style lang="scss" scoped>
page {
	height: 100%;
}
.login {
	height: 100%;
}
.wrapper {
  padding: 0 40rpx;
  height: 100%;
  position: relative;
  z-index: 99;
}

.bg-image {
  position: absolute;
  top:0;
  // top: -220rpx;
  right: 0;
  width: 100%;
  height: 862rpx;
  background-image: url("/static/loginBg.png");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: top right;
  z-index: 100;
  //   旋转90度
  // transform: rotate(25deg);
}

.title {
  padding-top: calc(104rpx);
  font-style: normal;
  line-height: 1;
  font-weight: 500;
  font-size: 48rpx;
  color: #333;
  text-align: center; /* Center the title */
}

.box-code {
  margin-top: 78rpx;
}

.desc,
.desc-light {
  font-size: 32rpx;
  line-height: 32rpx;
  color: #333333;
  margin-top: 40rpx;
}

.desc {
  color: #333;
}

.desc-light {
  color: #999999;

  > span {
    color: #333;
    margin-left: 8rpx;
  }
}

.carrier-text {
  font-size: 24rpx;
  color: #999999;
  margin-left: 10rpx;
}

.mobile-input-field {
  margin-top: 120rpx;
}

.code-input-wrapper {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 4rpx;
  border-bottom: 1rpx solid #eeeeee;
  padding-bottom: 20rpx;
}

.fetch-code-text {
  font-size: 32rpx;
  color: #ff5e00;
  margin-left: auto;
  margin-right: 20rpx;
}

.disable {
  // background: linear-gradient(90deg, #ffdcba 2.21%, #ffcfb2 99.86%);
  background: linear-gradient( 90deg, #FF5235 0%, #FF9500 100%);
  opacity: .5;
}

.fetch-gradient {
  // background: linear-gradient(57.72deg, #ff8a19 18.14%, #ff5e00 98.44%);
  background: linear-gradient( 90deg, #FF5235 0%, #FF9500 100%);
}

.register-login-btn {
  margin-top: 120rpx;
}

.one-click-btn-custom {
  background: linear-gradient(90deg, #ff8a19 0%, #ff5e00 100%);
  margin-top: 120rpx;
}

.btn {
  border-radius: 100px;
  width: 630rpx;
  height: 100rpx;
  font-size: 30rpx;
  line-height: 100rpx;
  text-align: center;
  color: #ffffff;
  margin-left: auto;
  margin-right: auto;
}

.tips {
  font-size: 24rpx;
  line-height: 40rpx;
  margin-top: 32rpx;
  width: 546rpx;
  color: #999999;

  > span {
    color: #FF5134;
  }
}

.privacy-terms-wrapper {
  margin-top: 32rpx;
  align-items: center;
  position: absolute;
  bottom: 80rpx;
}

.highlight-mobile {
  color: #333;
  margin-left: 8rpx;
}

.fetch-btn {
  width: 370rpx;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  background: #f2f2f2;
  border-radius: 100rpx;
  font-size: 28rpx;
  color: #999;

  margin: 71rpx auto 0 auto;
}

.other-login-options {
  display: flex;
  justify-content: space-between;
  width: 630rpx;
  margin-top: 40rpx;
  position: relative;
  left: 50%;
  transform: translateX(-50%);
}

.option-text {
  font-size: 28rpx;
  color: #333;
  // padding: 0 20rpx;
}
#h5_show {
  /* #ifdef H5 */
  display: none;
  /* #endif */
}

.login-list {
  display: none; 
}

.user-password-tips {
  display: none; 
}

.forgot-password {
  text-align: right;
  font-size: 24rpx;
  color: #999999;
  margin-top: 20rpx;
  padding-right: 20rpx;
}
/deep/ .u-checkbox__icon-wrap  {
  width: 28rpx !important;
  height: 28rpx !important;
}

.input-with-eye {
  display: flex;
  align-items: center;
  border-bottom: 1rpx solid #eeeeee;
  padding-right: 20rpx;
  // margin-top: 40rpx; // 保持和上面输入框间距一致
}
</style>
