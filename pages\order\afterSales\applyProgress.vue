<template>
  <view class="content">
    <view class="info-view">
      <view class="header-title-view">
        <view class="title">售后单号:</view>
        <view>{{ sn }}</view>
      </view>
      <view class="header-title-view" style="margin-top: 20rpx">
        <view class="title">申请时间:</view>
        <view>{{ createTime }}</view>
      </view>
    </view>
    <!-- <view class="info-view">
      <view class="header-title-view">
        <view>{{ serviceStatus }}</view>
      </view>
    </view> -->
    <view class="info-view">
      <view>
        <view v-if="logList.length != 0" class="timeline">
          <view
            v-for="(item, index) in logList"
            :key="index"
            class="timeline-item"
          >

            <view class="timeline-left">
              <!-- 节点 -->
              <u-icon
                :name="item.createBy=='SYSTEM' ? 'checkmark-circle-fill' : 'checkmark-circle'"
                :color="item.createBy=='SYSTEM' ? '#21A83D' : '#ccc'"
                size="28"
              />
              <!-- 线条（不是最后一个才显示） -->
              <view
                v-if="index !== logList.length - 1"
                class="timeline-line"
                :style="{ background: logList[index + 1].createBy=='SYSTEM' ? '#21A83D' : '#ccc' }"
              ></view>
            </view>
            <!-- 内容 -->
            <view class="timeline-content">
              <view class="u-order-desc">
                {{ item.message }}
                <text
                  v-if="item.highlightText"
                  style="color: #1aad19; margin-left: 8rpx"
                >
                  {{ item.highlightText }}
                </text>
                <text v-if="item.extra" style="color: #666; margin-left: 8rpx">
                  {{ item.extra }}
                </text>
              </view>
              <view class="timeline-time">{{ item.createTime }}</view>
            </view>
          </view>
        </view>
        <view v-else>
          <u-empty text="暂无审核日志"></u-empty>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      sn: "", //sn
      createTime: "", //创建时间
      logList: [
        {
          message: "售后申请",
          createTime: "2025-06-16 17:15:42",
          finished: true,
        },
        {
          message: "审核售后",
          createTime: "2025-06-16 17:15:42",
          highlightText: "同意",
          finished: true,
        },
        {
          message: "买家退货",
          createTime: "2025-06-16 17:15:42",
          extra: "物流单号为「FkJ88999890909090」",
          finished: false,
        },
        {
          message: "售后-商家收货",
          createTime: "2025-06-16 17:15:42",
          highlightText: "确认收货",
          finished: false,
        },
      ], //日志集合
      serviceStatus: "", //订单状态
    };
  },
  onLoad(options) {
    this.sn = options.sn;
    this.createTime = decodeURIComponent(options.createTime);
    this.serviceStatus = this.statusFilter(options.serviceStatus);
    this.logList = JSON.parse(decodeURIComponent(options.logs));
    console.log(this.logList);
    
  },
  methods: {
    statusFilter(val) {
      switch (val) {
        case "APPLY":
          return "售后服务申请成功，等待商家审核";
        case "PASS":
          return "售后服务申请审核通过";
        case "REFUSE":
          return "售后服务申请已被商家拒绝，如有疑问请及时联系商家";
        case "FULL_COURIER":
          return "申请售后的商品已经寄出，等待商家收货";
        case "STOCK_IN":
          return "商家已将售后商品入库";
        case "WAIT_FOR_MANUAL":
          return "等待平台进行人工退款";
        case "REFUNDING":
          return "商家退款中，请您耐心等待";
        case "COMPLETED":
          return "售后服务已完成，感谢您的支持";
        case "ERROR_EXCEPTION":
          return "系统生成新订单异常，等待商家手动创建新订单";
        case "CLOSED":
          return "售后服务已关闭";
        case "WAIT_REFUND":
          return "等待平台进行退款";
        default:
          return "";
      }
    },
    lastFinishedIndex() {
      let idx = -1;
      this.logList.forEach((item, i) => {
        if (item.finished) idx = i;
      });
      return idx;
    }
  },
};
</script>

<style lang="scss" scoped>
page,
.content {
  background: $page-color-base;
  height: 100%;
}
.content {
  padding: 20rpx 32rpx 0 32rpx;
}
.u-order-time {
  font-size: 24rpx;
  color: #999;
  margin: 20rpx 0;
}

.info-view {
  margin-bottom: 20rpx;
  border-radius: 20rpx;
  background-color: #fff;
  padding: 30rpx;

  .header-title-view {
    display: flex;
    flex-direction: row;
    align-items: center;
    font-weight: 400;
    font-size: 24rpx;
    color: #666666;
    .title {
      margin-right: 10rpx;

      // width: 160rpx;
    }
  }

  .steps-view {
    display: flex;
    flex-direction: row;
    align-items: center;
    color: #909399;
    border-bottom: 1px solid $page-color-base;
    margin-bottom: 10rpx;

    .title {
      width: 160rpx;
    }
  }
  // .u-icon {
  //   width: 20rpx;
  //   height: 20rpx;
  //   background: #21A83D;
  //   border-radius: 50%;
  //   display: flex;
  //   align-items: center;
  //   justify-content: center;
  // }
}

.u-time-axis::before {
  display: none;
}

.timeline {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  position: relative;
  padding: 10rpx 0 10rpx 0;
}
.timeline-item {
  display: flex;
  flex-direction: row;
  align-items: stretch; // 让左右两侧高度一致
  // min-height: 48rpx;
  // position: relative;
  // margin-bottom: 0;
}
.timeline-left {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 32rpx;
  position: relative;
  flex-shrink: 0;
  /* min-height: 106rpx; */ // 去掉
  // margin-top: 8rpx;
}
.timeline-dot {
  width: 18rpx;
  height: 18rpx;
  border-radius: 50%;
  background: #21A83D;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1;
  font-size: 14rpx;
  color: #fff;
  margin-top: 2rpx;
}
.dot-icon {
  font-size: 14rpx;
}
.timeline-line {
  width: 2rpx;
  flex: 1;
  /* min-height: 32rpx; */ // 可以去掉
  background: #21A83D;
  margin-top: 0;
}
.timeline-content {
  margin-left: 30rpx;
  padding-bottom: 2rpx;
  flex: 1;
  text-align: left;
  // padding-top: 30rpx;
}
.u-order-desc {
  width: 100%;
  font-size: 26rpx;
  color: #666666;
  font-weight: 500;
  display: flex;
}
.timeline-time {
  font-weight: 400;
  font-size: 24rpx;
  color: #666;
  margin-top: 12rpx;
}
</style>
