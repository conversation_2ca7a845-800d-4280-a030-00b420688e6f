<template>
  <view class="page-container">
    <scroll-view 
      scroll-y 
      class="scroll-container"
      @scrolltolower="onScrollToLower"
      :style="{ height: '100vh' }"
    >
      <view class="wrapper">
        <!-- uni 中不能使用 vue component 所以用if判断每个组件 -->
        <view v-for="(item, index) in pageData.list" :key="index">
          <!-- 搜索栏，如果在楼层装修顶部则会自动浮动，否则不浮动 -->
          <u-navbar class="navbar" 
            :background="background" 
            v-if="item.type == 'search'" 
            :border-bottom="false"
            :is-back="false"
            :is-fixed="index === 1 ? false : true">
            <customNavbar style="width: 100%" :refresh-flag="unreadCount"/>
            <!-- #ifndef H5 -->
            <!-- 扫码功能 不兼容h5 详情文档: https://uniapp.dcloud.io/api/system/barcode?id=scancode -->
            <!-- <div slot="right" class="navbar-right">
              <u-icon name="scan" @click="scan()" color="#666" size="50"></u-icon>
            </div> -->
            <!-- #endif -->
          </u-navbar>
          <enjoyCard v-if="item.type == 'search'" />
          <carousel v-if="item.type == 'carousel'" :res="item.options" />
          <titleLayout v-if="item.type == 'title'" :res="item.options" />
          <leftOneRightTwo v-if="item.type == 'leftOneRightTwo'" :res="item.options" />
          <leftTwoRightOne v-if="item.type == 'leftTwoRightOne'" :res="item.options" />
          <topOneBottomTwo v-if="item.type == 'topOneBottomTwo'" :res="item.options" />
          <topTwoBottomOne v-if="item.type == 'topTwoBottomOne'" :res="item.options" />
          <flexThree v-if="item.type == 'flexThree'" :res="item.options" />
          <flexFive v-if="item.type == 'flexFive'" :res="item.options" />
          <flexFour v-if="item.type == 'flexFour'" :res="item.options" />
          <flexTwo v-if="item.type == 'flexTwo'" :res="item.options" />
          <textPicture v-if="item.type == 'textPicture'" :res="item.options" />
          <menuLayout v-if="item.type == 'menu'" :res="item.options" />
          <flexOne v-if="item.type == 'flexOne'" :res="item.options" />
          <!-- <goods :enableBottomLoad="enableLoad" v-if="item.type == 'choiceness'" :res="item.options" /> -->
          <group v-if="item.type == 'group'" :res="item.options" />
          <notice v-if="item.type == 'notice'" :res="item.options" />
          <promotions v-if="item.type == 'promotionDetail'" :res="item.options" />
          <!-- <joinGroup v-if="item.type == 'joinGroup'" :res="item.options" /> -->
          <!-- <integral v-if="item.type == 'integral'" :res="item.options" /> -->
          <!-- <spike v-if="item.type == 'spike'" :res="item.options" /> -->
          
          <Guess 
            v-if="item.type == 'choiceness'"
            type="home" 
            :title="item.options.list[0].titleWay[0].title" 
            ref="guessComponent"
            :goodsList="item.options.list[0].listWay"
          />
        </view>
        <fetchCoupon ref='coupon' />
        
        <u-no-network @retry="init" @isConnected="isConnected"></u-no-network>
      </view>
    </scroll-view>
  </view>
</template>

<script>
import { messages } from "@/api/message.js";
// 引用组件
import tpl_banner from "@/pages/tabbar/home/<USER>/tpl_banner"; //导航栏模块
import tpl_title from "@/pages/tabbar/home/<USER>/tpl_title"; //标题栏模块
import tpl_left_one_right_two from "@/pages/tabbar/home/<USER>/tpl_left_one_right_two"; //左一右二模块
import tpl_left_two_right_one from "@/pages/tabbar/home/<USER>/tpl_left_two_right_one"; //左二右一模块
import tpl_top_one_bottom_two from "@/pages/tabbar/home/<USER>/tpl_top_one_bottom_two"; //上一下二模块
import tpl_top_two_bottom_one from "@/pages/tabbar/home/<USER>/tpl_top_two_bottom_one"; //上二下一模块
import tpl_flex_one from "@/pages/tabbar/home/<USER>/tpl_flex_one"; //单行图片模块
import tpl_flex_two from "@/pages/tabbar/home/<USER>/tpl_flex_two"; //两张横图模块
import tpl_flex_three from "@/pages/tabbar/home/<USER>/tpl_flex_three"; //三列单行图片模块
import tpl_flex_five from "@/pages/tabbar/home/<USER>/tpl_flex_five"; //五列单行图片模块
import tpl_flex_four from "@/pages/tabbar/home/<USER>/tpl_flex_four"; //四列单行图片模块
import tpl_text_picture from "@/pages/tabbar/home/<USER>/tpl_text_picture"; //文字图片模板
import tpl_menu from "@/pages/tabbar/home/<USER>/tpl_menu"; //五列菜单模块
import tpl_search from "@/pages/tabbar/home/<USER>/tpl_search"; //搜索栏
import tpl_group from "@/pages/tabbar/home/<USER>/tpl_group"; //
import tpl_goods from "@/pages/tabbar/home/<USER>/tpl_goods"; //商品分类以及分类中的商品
// 结束引用组件
import { getFloorData } from "@/api/home"; //获取楼层装修接口
import permission from "@/js_sdk/wa-permission/permission.js"; //权限工具类
import config from "@/config/config";
import { mapState } from 'vuex';

import tpl_notice from "@/pages/tabbar/home/<USER>/tpl_notice"; //标题栏模块
import tpl_promotions from "@/pages/tabbar/home/<USER>/tpl_promotions_detail"; //标题栏模块
import customNavbar from "@/pages/tabbar/home/<USER>/custom_navbar"; // Custom Navbar
import tpl_enjoy_card from "@/pages/tabbar/home/<USER>/tpl_enjoy_card"; //臻享卡模块
import storage from "@/utils/storage.js";
import fetchCoupon from '@/pages/tabbar/home/<USER>/fetch_coupon'
import Guess from '@/components/Guess.vue';
// import {receiveCoupons} from "@/api/members"

export default {
  data () {
    return {
      config,
      storage,
      showCp:true,
      pageData: "", //楼层页面数据
      isIos: "",
      enableLoad: false, //触底加载 针对于商品模块
      background: {
          // backgroundColor: "#001f3f",

          // 导航栏背景图 - 根据平台使用不同背景
          // #ifdef H5
          background: 'url(/static/navigator_bg1.png) no-repeat',
          // #endif
          // #ifdef APP-PLUS
          background: 'url(/static/navigator_app.png) no-repeat',
          // #endif
          // #ifdef MP-WEIXIN
          background: 'url(/static/navigator_bg1.png) no-repeat',
          // #endif
          // 还可以设置背景图size属性
          backgroundSize: '100%',

          // 渐变色
          // backgroundImage: 'linear-gradient(to right, rgb(255, 128, 0), rgb(255, 64, 0))' 
      },
      unreadCount:0
    };
  },
  computed: {
    ...mapState(['showEnjoyCard']),
  },
  components: {
    carousel: tpl_banner,
    titleLayout: tpl_title,
    leftOneRightTwo: tpl_left_one_right_two,
    leftTwoRightOne: tpl_left_two_right_one,
    topOneBottomTwo: tpl_top_one_bottom_two,
    topTwoBottomOne: tpl_top_two_bottom_one,
    flexThree: tpl_flex_three,
    flexFive: tpl_flex_five,
    flexFour: tpl_flex_four,
    flexTwo: tpl_flex_two,
    textPicture: tpl_text_picture,
    menuLayout: tpl_menu,
    search: tpl_search,
    flexOne: tpl_flex_one,
    goods: tpl_goods,
    group: tpl_group,
    notice: tpl_notice,
    promotions: tpl_promotions,
    fetchCoupon,
    Guess,
    customNavbar,
    enjoyCard: tpl_enjoy_card
  },

  mounted () {
    this.init();
    // #ifdef MP-WEIXIN
    // 小程序默认分享
    uni.showShareMenu({ withShareTicket: true });
    // #endif
   
  },
  methods: {
    getUnreadCount() {
      console.log("getUnreadCount");
      const params = {
        pageSize: 1,
        pageNumber: 1,
        memberId: this.$options.filters.isLogin().id,
        status: "UN_READY"
      };
      
      messages(params).then(res => {
        if (res.data.success && res.data.result && res.data.result.total) {
          this.unreadCount = res.data.result.total;
        } else {
          this.unreadCount = 0;
        }
      }).catch(error => {
        console.error('获取未读消息数量失败:', error);
        this.unreadCount = 0;
      });
    },
    fetchCoupon(){
       this.$refs.coupon.firstGetAuto();
    },
    /**
     * 实例化首页数据楼层
     */
    init () {
      this.pageData = "";
      getFloorData().then((res) => {
        if (res.data.success) {
          const result = JSON.parse(res.data.result.pageData)
          this.pageData = result;
          if (result.list.length) {
            // 如果最后一个装修模块是商品模块的话 默认启用自动加载
            result.list[result.list.length - 1] ? result.list[result.list.length - 1].model == 'goods' ? this.enableLoad = true : '' : ''
          }
        }
      });
    },
    // 是否有网络链接
    isConnected (val) {
      val ? this.init() : ''
    },

    /**
     * TODO 扫码功能后续还会后续增加
     * 应该实现的功能目前计划有：
     * 扫描商品跳转商品页面
     * 扫描活动跳转活动页面
     * 扫描二维码登录
     * 扫描其他站信息 弹出提示，返回首页。
     */
    // scanCode () {
    //   uni.scanCode({
    //     success: function (res) {
    //       let path = encodeURIComponent(res.result);



    //       if (path != undefined && path.indexOf("QR_CODE_LOGIN_SESSION") == 0) {
    //         console.log(path)
    //         //app扫码登录
    //         uni.navigateTo({
    //           url: "/pages/passport/scannerCodeLoginConfirm?token=" + path
    //         });
    //         return;
    //       }


    //       // WX_CODE 为小程序码
    //       if (res.scanType == "WX_CODE") {
    //         console.log(res);
    //         uni.navigateTo({
    //           url: `/${res.path}`,
    //         });
    //       } else {
    //         config.scanAuthNavigation.forEach((src) => {
    //           if (res.result.indexOf(src) != -1) {
    //             uni.navigateTo({
    //               url: `/${res.result.substring(src.length)}`,
    //             });
    //           } else {
    //             setTimeout(() => {
    //               uni.navigateTo({
    //                 url: "/pages/tabbar/home/<USER>" + path,
    //               });
    //             }, 100);
    //           }
    //         });
    //       }
    //     },
    //   });
    // },

    /**
     * 提示获取权限
     */
    // tipsGetSettings () {
    //   uni.showModal({
    //     title: "提示",
    //     content: "您已经关闭相机权限,去设置",
    //     success: function (res) {
    //       if (res.confirm) {
    //         if (this.isIos) {
    //           plus.runtime.openURL("app-settings:");
    //         } else {
    //           permission.gotoAppPermissionSetting();
    //         }
    //       }
    //     },
    //   });
    // },

    /**
     * 唤醒客户端扫码
     * 没权限去申请权限，有权限获取扫码功能
     */
    // async scan () {
    //   // #ifdef APP-PLUS
    //   this.isIos = plus.os.name == "iOS";
    //   // 判断是否是Ios
    //   if (this.isIos) {
    //     const iosFirstCamera = uni.getStorageSync("iosFirstCamera"); //是不是第一次开启相机
    //     if (iosFirstCamera !== "false") {
    //       uni.setStorageSync("iosFirstCamera", "false"); //设为false就代表不是第一次开启相机
    //       this.scanCode();
    //     } else {
    //       if (permission.judgeIosPermission("camera")) {
    //         this.scanCode();
    //       } else {
    //         // 没有权限提醒是否去申请权限
    //         this.tipsGetSettings();
    //       }
    //     }
    //   } else {
    //     /**
    //      * TODO 安卓 权限已经授权了，调用api总是显示用户已永久拒绝申请。人傻了
    //      * TODO 如果xdm有更好的办法请在 https://gitee.com/beijing_hongye_huicheng/lilishop/issues 提下谢谢
    //      */
    //     this.scanCode();
    //   }

    //   // #endif

    //   // #ifdef MP-WEIXIN
    //   this.scanCode();
    //   // #endif
    // },

    // 滚动到底部事件
    onScrollToLower() {
      console.log("滚动到底部");
      // 处理v-for循环中的ref数组
      if (this.$refs.guessComponent) {
        // 如果是数组，只调用最后一个Guess组件（通常是最底部的）
        if (Array.isArray(this.$refs.guessComponent)) {
          const lastComponent = this.$refs.guessComponent[this.$refs.guessComponent.length - 1];
          if (lastComponent && typeof lastComponent.getData === 'function') {
            lastComponent.getData();
          }
        } else {
          // 如果是单个组件
          if (this.$refs.guessComponent && typeof this.$refs.guessComponent.getData === 'function') {
            this.$refs.guessComponent.getData();
          }
        }
      }
    },
  },
  
};
</script>

<style scoped lang="scss">
.page-container {
  width: 100%;
  height: 100vh;
  position: relative;
}

.scroll-container {
  width: 100%;
  height: 100%;
}

.wrapper {
  min-height: 100%;
  box-sizing: border-box;
}
.navbar {
  background-size: 100% 100%;
  background-image: url("@/static/navigator_app.png");
}
.navbar-right {
  padding: 0 16rpx 0 0;
}
</style>
