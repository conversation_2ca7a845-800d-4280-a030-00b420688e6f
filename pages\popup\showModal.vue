<!-- pages/popup/showModal.vue -->
<template>
    <view>
        <!-- 使用uViewUI的u-modal -->
        <u-modal
            v-model="showModel"
            @confirm="confirm"
            @cancel="cancel"
            :show-cancel-button="true"
            :title="options.title"
            confirm-color="#FF5134"
            :show-title="options.title != null && options.title !== ''"
            :title-style="{
                'font-size': '40rpx',
            }"
        >
            <view class="modelContent">
                <view>
                    <!-- <u-icon
                        name="question-circle"
                        color="red"
                        size="38"
                        class="icon"
                    ></u-icon> -->
                    {{ options.content }}
                </view>
            </view>
        </u-modal>
    </view>
</template>
<script>
export default {
    data() {
        return {
            showModel: true,
            result: {
                confirm: false,
                cancel: false
            },
            modelTitle: '',
            options: {
                title: '',
                content: ''
            }
        }
    },
    onLoad(options) {
        // 获取配置
        this.options = options
    },
    methods: {
        closeModel() {
            // 执行操作后，发布事件同时返回原来页面
            uni.$emit('myModelResult', this.result)
            uni.navigateBack({
                delta: 1
            })
        },
        cancel() {
            this.result.cancel = true
            this.closeModel()
        },
        confirm() {
            this.result.confirm = true
            this.closeModel()
        }
    }
}
</script>
<style>
/* 需要设置页面背景透明 */
page {
    background: transparent;
}
</style>
<style lang="scss" scoped>
.modelContent {
    padding: 30rpx 30rpx;
    text-align: center;
    .icon {
        margin-right: 30rpx;
    }
}
/deep/ .u-model__footer__button{
  border-right: 1rpx solid #eee;
}
</style>

