<template>
  <div class="custom-navbar">
    <div class="left-text">
        <image
            src="/static/zhen.png"
            mode="scaleToFill"
        />
    </div>
    <div class="search-container" @tap="handleSearch">
      <u-search placeholder="输入商品名称搜索"  search-icon-color="#999" :show-action="false" 
      height="64"
      bg-color="#FFFFFF"
       @click="handleSearch"></u-search>
      <div class="search-button" @click="handleSearch">搜索</div>
    </div>
    <div class="right-icon">
      <!-- <u-icon name="bell" size="44" color="#ffffff" @tap="gotoNotice"></u-icon> -->
      <image
        src="/static/index/Information.png"
        mode="scaleToFill"
        style="width: 40rpx;height: 40rpx;"
        @tap="gotoNotice"
      />
      <view v-if="refreshFlag > 0" class="unread-dot"></view>
    </div>
  </div>
</template>

<script>
import { messages } from "@/api/message.js";

export default {
  data() {
    return {
      unreadCount: 0
    };
  },
  props: {
    refreshFlag: {
      type: Number,
      default: 0
    }
  },
  watch: {
    refreshFlag(newVal, oldVal) {
      console.log('refreshFlag', newVal);
      
    }
  },
  mounted() {
    
    // this.getUnreadCount()
  },
 

  methods: {
    handleSearch() {
      uni.navigateTo({
        url: `/pages/navigation/search/searchPage`,
      });
    },
    gotoNotice() {
      uni.navigateTo({
        url: "/pages/tabbar/home/<USER>",
      });
    },

    getUnreadCount() {
      console.log("getUnreadCount");
      const params = {
        pageSize: 1,
        pageNumber: 1,
        memberId: this.$options.filters.isLogin().id,
        status: "UN_READY"
      };
      
      messages(params).then(res => {
        if (res.data.success && res.data.result && res.data.result.total) {
          this.unreadCount = res.data.result.total;
        } else {
          this.unreadCount = 0;
        }
      }).catch(error => {
        console.error('获取未读消息数量失败:', error);
        this.unreadCount = 0;
      });
    },
   
  },
};
</script>

<style lang="scss" scoped>
.custom-navbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 32rpx;
  // height: 100rpx; 
}

.left-text {
  image {
    width: 140rpx;
    height: 46rpx;
    margin-right: 36rpx;
    display: block;
  }
}

.search-container {
  flex: 1;
  display: flex;
  align-items: center;
  background-color: #fff;
  border-radius: 32rpx;
  overflow: hidden;
  /* #ifdef APP-PLUS */
  margin-bottom: 20rpx;
  /* #endif */
}

.search-button {
//   background-color: #ff5500; 
  color: #ff5500;
  padding: 0 20rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  white-space: nowrap;
  font-size: 28rpx;
}

.right-icon {
  margin-left: 32rpx;
  position: relative;
}

.unread-dot {
  position: absolute;
  top: 0rpx;
  right: 2rpx;
  width: 16rpx;
  height: 16rpx;
  background-color: #ff4757;
  border-radius: 50%;
  border: 2rpx solid #ffffff;
}
</style> 