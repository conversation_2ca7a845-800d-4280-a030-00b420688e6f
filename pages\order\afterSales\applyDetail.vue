<template>
  <view v-if="serviceDetail" class="service-detail">
    <view class="after-sales-goods-detail-view">
      <!-- <view class="header">
        <view>
          本次售后服务将由
          <text class="seller-name">{{ serviceDetail.storeName }}</text>
          为您提供
        </view>
      </view> -->
      <view class="apply-info-view">
        <view>
          <view class="status-info-box">
            <view class="status-val">{{
              serviceDetail.serviceStatus | serviceStatusList
            }}</view>

            <!-- <view class="status-tip">{{
              serviceDetail.serviceStatus | statusFilter
            }}</view> -->
            <view class="log-box-top" @click="onProgress()">
              <view class="top01">
                <view>审核详情</view>
                <view class="log-first-show" v-if="logs[0]">
                  申请售后：{{ getAfterSaleSn(logs[0].message) }}
                </view>
              </view>
              <u-icon
                name="arrow-right"
                size="30"
                color="#999"
              ></u-icon>
            </view>
          </view>
        </view>
      </view>
    </view>
    <view>
      <view class="goods-info-card">
        <view class="goods-img">
          <u-image
            border-radius="8"
            width="148rpx"
            height="148rpx"
            :src="serviceDetail.goodsImage"
          ></u-image>
        </view>
        <view class="goods-info-main">
          <view class="goods-title">{{ serviceDetail.goodsName }}</view>
          <view class="goods-specs">{{  formatSpecs(serviceDetail.specs) }}</view>
          <view class="goods-bottom-row">
            <view class="goods-price">￥{{ serviceDetail.flowPrice | unitPrice }}</view>
            <view class="goods-num">申请售后数量：{{ serviceDetail.num }}</view>
          </view>
        </view>
      </view>
    </view>
    <view class="apply-detail-view">
      <view class="detail-item">
        <view class="title">服务单号:</view>
        <view class="value">{{ serviceDetail.sn }}</view>
      </view>
      <view class="detail-item">
        <view class="title">订单编号:</view>
        <view class="value">{{ serviceDetail.orderSn }}</view>
      </view>
      <view class="detail-item" v-if="serviceDetail.new_order_sn">
        <view class="title">新订单编号:</view>
        <view class="value">{{ serviceDetail.new_order_sn }}</view>
      </view>
      <view class="detail-item">
        <view class="title">服务类型:</view>
        <view class="value">{{
          serviceTypeList[serviceDetail.serviceType]
        }}</view>
      </view>
      <view class="detail-item">
        <view class="title">申请原因:</view>
        <view class="value">{{ reason }}</view>
      </view>
      <!-- <view class="detail-item" v-if="serviceDetail.apply_vouchers">
				<view class="title">申请凭证:</view>
				<view class="value">{{ serviceDetail.apply_vouchers }}</view>
			</view> -->
      <view class="detail-item" v-if="serviceDetail.problemDesc">
        <view class="title">问题描述:</view>
        <view class="value">{{ serviceDetail.problemDesc }}</view>
      </view>

      <view
        class="detail-item"
        v-if="
          serviceDetail.afterSaleImage &&
          serviceDetail.afterSaleImage.split(',').length != 0
        "
      >
        <view
          v-for="(img, index) in serviceDetail.afterSaleImage.split(',')"
          :key="index"
          class="img-view"
        >
          <u-image
            width="148"
            height="148"
            :src="img"
            mode="aspectFill"
            border-radius="8"
            @click="preview(serviceDetail.afterSaleImage.split(','), index)"
          ></u-image>
        </view>
      </view>
      
    </view>
    <view class="apply-detail-view" >
      
      <!-- 如果服务类型为退款则不显示 -->
      <view
        class="detail-item"
        v-if="
          serviceDetail.serviceType != 'RETURN_MONEY' &&
          serviceDetail.serviceStatus != 'APPLY'
        "
      >
        <view class="title">收货地址:</view>
        <view class="value">
          <span v-if="storeAfterSaleAddress.salesConsigneeAddressPath">{{
            storeAfterSaleAddress.salesConsigneeAddressPath
          }}</span>
          <span v-if="storeAfterSaleAddress.salesConsigneeDetail">{{
            storeAfterSaleAddress.salesConsigneeDetail
          }}</span>
        </view>
      </view>
      <!-- 如果服务类型为退款则不显示 -->
      <view
        class="detail-item"
        v-if="
          serviceDetail.serviceType != 'RETURN_MONEY' &&
          serviceDetail.serviceStatus != 'APPLY'
        "
      >
        <view class="title">联系人:</view>
        <view class="value">{{
          storeAfterSaleAddress.salesConsigneeName
        }}</view>
      </view>
      <!-- 如果服务类型为退款则不显示 -->
      <view
        class="detail-item"
        v-if="
          serviceDetail.serviceType != 'RETURN_MONEY' &&
          serviceDetail.serviceStatus != 'APPLY'
        "
      >
        <view class="title">联系方式:</view>
        <view class="value">{{
          storeAfterSaleAddress.salesConsigneeMobile || "" | secrecyMobile
        }}</view>
      </view>
      <view v-if="refundShow">
        <view class="detail-item">
          <view class="title">退款金额:</view>
          <view class="value">{{
            serviceDetail.flowPrice | unitPrice("￥")
          }}</view>
        </view>
        <view class="detail-item" v-if="serviceDetail.agree_price">
          <view class="title">同意退款:</view>
          <view class="value">{{
            serviceDetail.agree_price | unitPrice("￥")
          }}</view>
        </view>
        <view class="detail-item" v-if="serviceDetail.actual_price">
          <view class="title">实际退款:</view>
          <view class="value">{{
            serviceDetail.actual_price | unitPrice("￥")
          }}</view>
        </view>
        <view class="detail-item" v-if="serviceDetail.actual_price">
          <view class="title">退款时间:</view>
          <view class="value">{{
            serviceDetail.refund_time | unixToDate
          }}</view>
        </view>
        <view class="detail-item" v-if="serviceDetail.refund_price !== 0">
          <view class="title">退款方式:</view>
          <view class="value">{{
            serviceDetail.refundWay | refundWayFilter
          }}</view>
        </view>
        <view
          class="detail-item"
          v-if="accountShow && serviceDetail.refund_price != 0"
        >
          <view class="title">账户类型:</view>
          <view class="value">{{
            serviceDetail.accountType | accountTypeFilter
          }}</view>
        </view>
        <view
          class="detail-item"
          v-if="
            accountShow && !bankShow && serviceDetail.actualRefundPrice != 0
          "
        >
          <view class="title">退款账号:</view>
          <view class="value">{{ serviceDetail.bankAccountNumber }}</view>
        </view>
        <view class="detail-item" v-if="bankShow">
          <view class="title">银行名称:</view>
          <view class="value">{{ serviceDetail.bankAccountName }}</view>
        </view>
        <view class="detail-item" v-if="bankShow">
          <view class="title">银行账号:</view>
          <view class="value">{{ serviceDetail.bankAccountNumber }}</view>
        </view>
        <view class="detail-item" v-if="bankShow">
          <view class="title">银行开户名:</view>
          <view class="value">{{ serviceDetail.bankAccountName }}</view>
        </view>
        <view class="detail-item" v-if="bankShow">
          <view class="title">银行开户行:</view>
          <view class="value">{{ serviceDetail.bankDepositName }}</view>
        </view>
        <view class="detail-item" v-if="serviceDetail.mlogisticsName">
          <view class="title">回寄快递:</view>
          <view class="value">{{ serviceDetail.mlogisticsName }}</view>
        </view>
        <view class="detail-item" v-if="serviceDetail.mlogisticsNo">
          <view class="title">回寄运单号:</view>
          <view class="value">{{ serviceDetail.mlogisticsNo }}</view>
        </view>
        <view class="detail-item" v-if="serviceDetail.mDeliverTime">
          <view class="title">回寄时间:</view>
          <view class="value">{{ serviceDetail.mDeliverTime }}</view>
        </view>
      </view>
    </view>
    <view style="height: 100rpx;" ></view>
  </view>
</template>

<script>
import {
  getServiceDetail,
  getStoreAfterSaleAddress,
  getAfterSaleLog,
  getAfterSaleReason,
} from "@/api/after-sale.js";
export default {
  data() {
    return {
      reason: "", //申请原因
      serviceTypeList: {
        // 售后类型
        CANCEL: "取消",
        RETURN_GOODS: "退货",
        EXCHANGE_GOODS: "换货",
        RETURN_MONEY: "退款",
      },
      serviceDetail: {}, // 售后详情
      logs: [], //日志
      goodsList: [], //商品列表
      storeAfterSaleAddress: {}, //售后地址
      refundShow: false, //退款开关
      accountShow: false, //账户显示
      bankShow: false, //银行显示
      sn: "", //订单sn
    };
  },
  onLoad(options) {
    uni.setNavigationBarTitle({
      title: "服务单详情",
    });
    this.sn = options.sn;
    this.loadDetail();
    this.getAddress();
    this.getLog(options.sn);
  },
  filters: {
    /**
     * 售后状态信息
     */
    statusFilter(val) {
      switch (val) {
        case "APPLY":
          return "售后服务申请成功，等待商家审核";
        case "PASS":
          return "售后服务申请审核通过";
        case "REFUSE":
          return "售后服务申请已被商家拒绝，如有疑问请及时联系商家";
        case "FULL_COURIER":
          return "申请售后的商品已经寄出，等待商家收货";
        case "STOCK_IN":
          return "商家已将售后商品入库";
        case "WAIT_FOR_MANUAL":
          return "等待平台进行人工退款";
        case "REFUNDING":
          return "商家退款中，请您耐心等待";
        case "COMPLETED":
          return "售后服务已完成，感谢您的支持";
        case "ERROR_EXCEPTION":
          return "系统生成新订单异常，等待商家手动创建新订单";
        case "CLOSED":
          return "售后服务已关闭";
        case "WAIT_REFUND":
          return "等待平台进行退款";
        default:
          return "";
      }
    },

    /**
     * 退款信息
     */
    refundWayFilter(val) {
      switch (val) {
        case "OFFLINE":
          return "账户退款";
        case "OFFLINE":
          return "线下退款";
        case "ORIGINAL":
          return "原路退回";
        default:
          return "";
      }
    },
    /**
     * 账户信息
     */
    accountTypeFilter(val) {
      switch (val) {
        case "WEIXINPAY":
          return "微信";
        case "ALIPAY":
          return "支付宝";
        case "BANK_TRANSFER":
          return "银行卡";
        default:
          return "";
      }
    },
  },
  methods: {
    /**
     * 点击图片放大或保存
     */
    preview(urls, index) {
      uni.previewImage({
        current: index,
        urls: urls,
        longPressActions: {
          itemList: ["保存图片"],
          success: function (data) {},
          fail: function (err) {},
        },
      });
    },
    formatSpecs(specs) {
      if (!specs) return '无规格';
      // 如果是字符串，尝试解析
      if (typeof specs === 'string') {
        try {
          specs = JSON.parse(specs);
        } catch (e) {
          return '无规格';
        }
      }
      if (typeof specs !== 'object') return '无规格';
      const arr = Object.keys(specs)
        .filter(key => key !== 'images' && specs[key] !== undefined && specs[key] !== null && specs[key] !== '')
        .map(key => `${key}: ${specs[key]}`);
      return arr.length ? arr.join('；') : '无规格';
    },

    /**
     * 获取地址信息
     */
    getAddress() {
      getStoreAfterSaleAddress(this.sn).then((res) => {
        if (res.data.success) {
          this.storeAfterSaleAddress = res.data.result;
        }
      });
    },

    /**
     * 获取日志
     */
    getLog(sn) {
      getAfterSaleLog(sn).then((res) => {
        this.logs = res.data.result;
      });
    },

    /**
     * 获取申请原因
     */
    getReasonList(serviceType) {
      getAfterSaleReason(serviceType).then((res) => {
        if (res.data.success) {
          // 1357583466371219456
          this.reason = this.serviceDetail.reason;
        }
      });
    },

    /**
     * 初始化详情
     */
    loadDetail() {
      // uni.showLoading({
      //   title: "加载中",
      // });
      getServiceDetail(this.sn).then((res) => {
        if (this.$store.state.isShowToast) {
          // uni.hideLoading();
        }
        this.serviceDetail = res.data.result;
        if (
          this.serviceDetail.serviceType == "RETURN_GOODS" ||
          this.serviceDetail.serviceType === "RETURN_MONEY"
        ) {
          this.refundShow = true;
        }

        this.accountShow =
          (this.serviceDetail.serviceType === "RETURN_GOODS" ||
            this.serviceDetail.serviceType === "ORDER_CANCEL") &&
          this.serviceDetail.refundWay === "OFFLINE";

        this.bankShow =
          this.serviceDetail.accountType === "BANK_TRANSFER" &&
          this.serviceDetail.refundWay === "OFFLINE" &&
          ((this.serviceDetail.serviceType === "RETURN_GOODS") |
            (this.serviceDetail.serviceType === "ORDER_CANCEL") ||
            this.serviceDetail.serviceType === "RETURN_MONEY");

        this.getReasonList(this.serviceDetail.serviceType);
      });
    },

    /**
     * 访问商品详情
     */
    navgiateToGoodsDetail(item) {
      uni.navigateTo({
        url: `/pages/product/goods?id=${item.id}&goodsId=${item.goodsId}`,
      });
    },

    /**
     * 进度
     */
    onProgress() {
      uni.navigateTo({
        url: `./applyProgress?sn=${
          this.serviceDetail.sn
        }&createTime=${encodeURIComponent(this.serviceDetail.createTime)}
         &logs=${encodeURIComponent(JSON.stringify(this.logs))}&serviceStatus=${
          this.serviceDetail.serviceStatus
        }`,
      });
    },

    getAfterSaleSn(message) {
      if (!message) return "";
      const match = message.match(/\[([A-Za-z0-9]+)\]/);
      return match ? match[1] : "";
    },
  },
};
</script>

<style lang="scss" scoped>
page,
.content {
  background: $page-color-base;
  height: 100%;
}
.after-sales-goods-detail-view {
  background-color: #fff;
  .header {
    background-color: #f7f7f7;
    color: #999999;
    font-size: 22rpx;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    line-height: 70rpx;
    .header-text {
      background-color: #999999;
      padding: 10rpx 30rpx;
      border-radius: 50rpx;
    }
    .seller-name {
      color: $main-color;
    }
  }
  .apply-info-view {
    // background: $page-color-base;
    width: 750rpx;
    height: 248rpx;
    background: linear-gradient(180deg, #ff6634 0%, rgba(255, 102, 52, 0) 100%);
    border-radius: 0rpx 0rpx 0rpx 0rpx;
  }

  .goods-item-view {
    display: flex;
    flex-direction: row;
    padding: 20rpx 30rpx;
    background-color: #eef1f2;

    .goods-num {
      width: 60rpx;
      color: $main-color;
    }
  }
}
.apply-detail-view {
  background-color: #fff;
  padding: 32rpx;
  color: #666666;
  width: 686rpx;
  margin: 20rpx auto;
  border-radius: 20rpx;
  .detail-item {
    // padding: 12rpx;
    display: flex;
    // flex-wrap: wrap;
    flex-direction: row;
    // align-items: center;
    font-size: 24rpx;
    margin-bottom: 20rpx;
    .title {
      padding-right: 10rpx;
      flex-shrink: 0;
      // width: 140rpx;
    }
    .value {
      // padding-left: 40rpx;
      word-break: break-all;
      white-space: normal;
    }
  }
  .detail-item:last-child {
    margin-bottom: 0;

  }
}

.log-box-bottom {
  height: 120rpx;
  flex-direction: column;
  background-color: rgb(247, 247, 247);
}
.log-box-top {
  width: 686rpx;
  background: #ffffff;
  border-radius: 20rpx;
  margin: 40rpx auto 0;
  position: relative;
  z-index: 19;
  padding: 32rpx;
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
  .top01 {
    width: 90%;
    font-family: PingFangSC-Regular;
    font-weight: 500;
    font-size: 28rpx;
    color: #333333;
    overflow: hidden;
    text-overflow: ellipsis;
    overflow-wrap: break-word;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    white-space: normal;

    .log-first-show {
      flex-direction: row;
      font-family: PingFangSC-Regular;
      font-weight: 400;
      font-size: 24rpx;
      color: #666666;
      margin-top: 20rpx;
    }
  }
}
.status-val {
  padding: 40rpx 0 0 60rpx;
  font-weight: 500;
  font-size: 36rpx;
  color: #ffffff;
}
.status-tip {
  font-size: 24rpx;
}
.info-box {
  padding-right: 40rpx 0rpx;
  background-color: #eef1f2;
}
.service-detail {
  background: #f7f7f7;
}
.goods-info {
  padding-left: 30rpx;
  flex: 1;
  background: #fff;
  display: flex;
  .goods-title {
    margin-bottom: 10rpx;
    font-size: 28rpx;
    color: $font-color-dark;
  }
  .goods-specs {
    font-size: 24rpx;
    margin-bottom: 10rpx;
    color: #cccccc;
  }
  .goods-price {
    display: flex;
    justify-content: space-between;
    font-size: 24rpx;
    color: #999999;
  }
  .price {
    color: $light-color;
  }
}
.goods-info-card {
  width: 686rpx;
  margin: 50rpx auto 24rpx auto;
  /* #ifdef H5 */
  margin: 60rpx auto 24rpx auto;
  /* #endif */
  background: #fff;
  border-radius: 20rpx;
  box-shadow: 0 8rpx 32rpx 0 rgba(0,0,0,0.06);
  display: flex;
  align-items: center;
  padding: 24rpx;
}
.goods-img {
  width: 148rpx;
  height: 148rpx;
  border-radius: 8rpx;
  overflow: hidden;
  flex-shrink: 0;
}
.goods-info-main {
  flex: 1;
  margin-left: 20rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.goods-title {
  font-size: 32rpx;
  color: #333;
  font-weight: 400;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  white-space: normal;
  display: -webkit-box;

}
.goods-specs {
  font-size: 22rpx;
  color: #bbb;
  margin-bottom: 12rpx;
}
.goods-bottom-row {
  display: flex;
  align-items: flex-end;
  justify-content: space-between;
}
.goods-price {
  font-weight: 500;
  font-size: 28rpx;
  color: #FF5134;
}
.goods-num {
  font-weight: 400;
  font-size: 20rpx;
  color: #666666;
}
.img-view {
  margin-right: 10rpx;
}
</style>
