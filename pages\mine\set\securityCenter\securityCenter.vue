<template>
  <view class="securityCenter-bg">
    <view class="card">
      <u-cell-item title="账户密码" value="修改账户密码" @click="navigateTo('/pages/mine/set/securityCenter/updatePwdTab')"
        :value-style="{ fontWeight: 400, fontSize: '28rpx', color: '#999999' }"
      />
      <!-- <u-cell-item title="支付密码" value="修改支付密码" @click="navigateTo('/pages/mine/set/securityCenter/updatePayPwdTab')" /> -->
      <u-cell-item title="账户注销" :border-bottom="false" @click="zhuxiao" />
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      mobile: "", //存储手机号
    };
  },

  methods: {
    goBack() {
      uni.navigateBack();
    },
    zhuxiao() {
      // uni.showModal({
      //   title: "警告",
      //   content: "您确定要注销当前账号吗？",
      //   confirmText: "确定注销",
      //   confirmColor: "#FF0000",
      //   cancelText: "取消",
      //   success: (res) => {
      //     if (res.confirm) {
      //       uni.showModal({
      //         title: "谨慎操作",
      //         content: "再次向您确认，您确定要注销当前账号吗？",
      //         confirmText: "坚持注销",
      //         confirmColor: "#FF0000",
      //         cancelText: "取消",
      //         success: (res) => {
      //           if (res.confirm) {
      //             uni.showToast({
      //               title: "您的注销申请已经提交，待管理员审核后。会自动注销当前账号",
      //               duration: 10000,
      //             });
      //           }
      //         },
      //       });
      //     }
      //   },
      // });
      uni.navigateTo({ url: '/pages/mine/set/securityCenter/accountCancel' });
    },
    navigateTo(url) {
      uni.navigateTo({ url });
    },
  },
};
</script>

<style lang="scss" scoped>
.securityCenter-bg {
  background: #fafafa;
  height: 100%;
  overflow: hidden;
}

.card {
  margin: 40rpx 24rpx 0 24rpx;
  border-radius: 16rpx;
  background: #fff;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.03);
  overflow: hidden;
}
/deep/ .u-cell {
  align-items: center;
}
/deep/ .u-cell__value {
  color: #cccccc !important;
}
</style>
