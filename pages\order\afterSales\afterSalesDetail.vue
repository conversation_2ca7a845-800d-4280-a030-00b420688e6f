<template>
  <view class="content">
    <u-form :model="form" ref="uForm">
      <view class="after-sales-goods-detail-view">
        <!-- <view class="header">
          <view>
            本次售后服务将由
            <text class="seller-name">{{ sku.storeName }}</text>
            为您提供
          </view>
        </view> -->
        <view>
          <view class="goods-card" 
            @click="gotoGoodsDetail(applyInfo.goods_id)">
            <view class="goods-img">
              <u-image border-radius="6" width="84rpx" height="84rpx" :src="applyInfo.image"></u-image>
            </view>
            <view class="goods-info">
              <view class="goods-title">{{ applyInfo.goodsName }}</view>
              <!-- <view class="goods-price" style="color: #FF5134;">¥{{ sku.goodsPrice }}</view> -->
              <view class="goods-specs">{{ formatSpecs(applyInfo.specs) }}</view>
            </view>
            <view class="goods-num">x{{ applyInfo.num }}</view>
          </view>
        </view>
        <!-- <view class="after-num">
          <view>申请数量</view>
          <view>
            <u-number-box :value="parseInt(form.num)" disabled-input :min="1" :max="parseInt(sku.num)" bg-color="#fff"
              @change="valChange"></u-number-box>
          </view>
        </view> -->
      </view>
      <view class="body-view">
        <!-- 退款原因 -->
        
        <view class="refund-info-card">
         <view class="refund-row">
            <view class="refund-label">申请类型</view>
            <view class="refund-type-btn">我要退款</view>
          </view>
          
          <view class="refund-row" v-if="form.serviceType === 'RETURN_GOODS'">
            <view class="refund-label">货物状态</view>
            <view class="refund-type-btn" @click="onShowGoodsStatusModal">
              <text :class="{'refund-reason-placeholder': !form.goodsStatus}">
                {{ form.goodsStatus || '点击选择收货状态' }}
              </text>
              <u-icon name="arrow-right" color="#bbb" size="24"></u-icon>
            </view>
          </view>
          <view class="refund-row refund-link" @click="onShowReasonModal">
            <view class="refund-label">退款原因</view>
            <view class="refund-reason">
              <text :class="{'refund-reason-placeholder': !form.reason}">{{ form.reason || '点击选择申请原因' }}</text>
              <u-icon name="arrow-right" color="#bbb" size="24"></u-icon>
            </view>
          </view>
          <view class="refund-row" v-if="applyInfo.penaltyInfo && applyInfo.penaltyInfo.penaltyPrice">
            <view class="refund-label">违约金</view>
            <view class="refund-penalty">¥{{ applyInfo.penaltyInfo.penaltyPrice || '25.00' }}</view>
          </view>
        </view>
        <!-- <view class="desc">本单使用的"7元店铺无门槛劵"将退回给您</view> -->
        <!-- 申请金额 -->
        <view class="apply-amount">
          <view>申请金额</view>
          <view class="amount2">¥{{flowPrice ? Number(flowPrice).toFixed(2) : '0.00' }}</view>
          <view v-if="applyInfo.useCoupons && applyInfo.useCoupons.length">
            <view class="desc" v-for="(desc, idx) in applyInfo.useCoupons" :key="idx">
              本单使用的"{{ desc }}"优惠券将退回给您
            </view>
          </view>
        </view>

        <!-- 退货说明模块 -->
        <view class="rule-list">
          <view class="rule-item" @click="gotoFreightRule">
            <view>退货运费规则</view>
            <u-icon name="arrow-right" color="#bbb" size="28"></u-icon>
          </view>
          <view class="rule-item" @click="gotoPenaltyRule">
            <view>退货违约金说明</view>
            <u-icon name="arrow-right" color="#bbb" size="28"></u-icon>
          </view>
        </view>

        <!-- 申请说明模块 -->
        <view class="apply-desc-card">
          <view class="desc-header">
            <view class="desc-limit">您还可以输入{{ 170 - (form.problemDesc ? form.problemDesc.length : 0) }}字</view>
          </view>
          <!-- <view class="desc-label">请您详细填写申请说明accept="image,video"</view> -->
          <textarea
            class="desc-textarea"
            v-model="form.problemDesc"
            maxlength="170"
            placeholder="请填写申请说明，最多170字"
            :auto-height="true"
          />
          <view class="desc-upload">
            <u-upload
              :header="{ accessToken: storage.getAccessToken() }"
              :action="action"
              width="150"
              @on-uploaded="onUploaded"
              :max-count="5"
              :show-progress="false"
              :file-list="fileList"
               accept="image"
               :custom-btn="true"
               :auto-upload="false"
               ref="uUpload"
            >
              <view slot="addBtn" class="upload-btn">
                <image
                  src="/static/xj.png"
                  mode="scaleToFill"
                />
                <view class="upload-text">图片</view>
              </view>
            </u-upload>
          </view>
        </view>

        <!-- 联系电话模块 -->
        <!-- <view class="contact-card" @click="callPhone(applyInfo.phone || '***********')">
          <view class="contact-label">联系电话</view>
          <view class="contact-value">{{ applyInfo.phone || '***********' }}</view>
        </view> -->

        <view class="opt-view" v-if="false">
          <u-form-item label="退款方式" :label-width="150">
            <u-input :value="
                applyInfo.refundWay == 'ORIGINAL' ? '原路退回' : '账号退款'
              " type="text" input-align="right" :disabled="true" />
          </u-form-item>
          <view v-if="
              applyInfo.accountType === 'BANK_TRANSFER' &&
              applyInfo.applyRefundPrice != 0">
            <u-form-item label="银行开户行" :label-width="150">
              <u-input v-model="form.bankDepositName" type="text" input-align="right" placeholder="请输入银行开户行" />
            </u-form-item>
            <u-form-item label="银行开户名" :label-width="150">
              <u-input v-model="form.bankAccountName" type="text" input-align="right" placeholder="请输入银行开户名" />
            </u-form-item>
            <u-form-item label="银行账号" :label-width="150">
              <u-input v-model="form.bankAccountNumber" type="text" input-align="right" placeholder="请输入银行账号" />
            </u-form-item>
          </view>

          <u-form-item label="返回方式" :label-width="150">
            <u-input type="text" input-align="right" value="快递至第三方卖家" :disabled="true" />
          </u-form-item>
        </view>

        <!-- <view class="opt-tip">提交服务单后，售后专员可能与您电话沟通，请保持手机畅通</view> -->
      </view>
    </u-form>
    <view style="height: 200rpx;"></view>
    <view class="submit-view">
      <view  class="submit-btn" v-if="applyInfo.refundWay" 
        @click="onSubmit">确认</view>
    </view>
    <!-- <u-select mode="single-column" :list="reasonList" v-model="reasonSelectShow" @confirm="reasonSelectConfirm">
    </u-select> -->
    <u-select mode="single-column" :list="typeList" v-model="typeSelectShow" @confirm="typeSelectConfirm"></u-select>
    <u-select mode="single-column" :list="returnList" v-model="returnSelectShow" @confirm="returnSelectConfirm">
    </u-select>
    <u-toast ref="uToast" />
    <!-- 申请原因弹窗 -->
    <view v-if="reasonSelectShow" class="reason-modal-mask">
      <view class="reason-modal">
        <view class="reason-modal-header">
          <view class="reason-modal-title">请选择申请原因</view>
          <u-icon class="close_btn" name="close" size="36" color="#bbb" @click="reasonSelectShow = false"></u-icon>
        </view>
        <view class="reason-modal-list">
          <view
            v-for="(item, idx) in reasonList"
            :key="item.value"
            class="reason-modal-item"
            
            @click="selectReason(idx)"
          >
            <view class="reason-modal-label">{{ item.label }}</view>
            <view class="reason-modal-radio">
              <image
                v-if="selectedReasonIndex === idx"
                src="/static/gou.png"
                mode="scaleToFill"
              />
              <view v-else></view>
            </view>
          </view>
        </view>
      </view>
    </view>
    <!-- 货物状态弹窗 -->
    <view v-if="goodsStatusSelectShow" class="reason-modal-mask">
      <view class="reason-modal">
        <view class="reason-modal-header">
          <view class="reason-modal-title">请选择货物状态</view>
          <u-icon class="close_btn" name="close" size="36" color="#bbb" @click="goodsStatusSelectShow = false"></u-icon>
        </view>
        <view class="reason-modal-list">
          <view
            v-for="(item, idx) in goodsStatusList"
            :key="item.value"
            class="reason-modal-item"
            @click="selectGoodsStatus(idx)"
          >
            <view class="reason-modal-label">{{ item.label }}</view>
            <view class="reason-modal-radio">
              <image
                v-if="selectedGoodsStatusIndex === idx"
                src="/static/gou.png"
                mode="scaleToFill"
              />
              <view v-else></view>
            </view>
          </view>
        </view>
      </view>
    </view>
    <view v-if="showLeaveModal" class="leave-modal-mask">
      <view class="leave-modal-box">
        <view class="leave-modal-title">确认取消售后申请吗？</view>
        <view class="leave-modal-btns">
          <view class="leave-modal-btn cancel" @click="showLeaveModal = false" >取消</view>
          <view class="leave-modal-btn confirm" @click="giveUpPay">确认</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import {
  getAfterSaleReason,
  applyReturn,
  getAfterSaleInfo,
} from "@/api/after-sale";

import city from "@/components/m-city/m-city";
import { upload } from "@/api/common.js";
import { checkBankno } from "@/utils/Foundation";
import storage from "@/utils/storage.js";
export default {
  component: {
    city,
  },
  data() {
    return {
      storage,
      placeholderStyle: 'font-size: 32rpx;color: #999',
      list: [{ id: "", localName: "请选择", children: [] }],
      action: upload, //图片上传数据
      fileList: [],
      sn: "",
      sku: {},
      typeValue: 0,
      value: "",
      type: "textarea",
      border: true,
      //退款原因 弹出框
      reasonSelectShow: false,
      reasonList: [],
      //退款方式为账号退款 账号类型弹出框
      typeSelectShow: false,
      typeList: [
        {
          value: "ALIPAY",
          label: "支付宝",
        },
        {
          value: "WEIXINPAY",
          label: "微信",
        },
        {
          value: "BANK_TRANSFER",
          label: "银行卡",
        },
      ],
      //返回方式
      returnSelectShow: false,
      returnList: [
        {
          value: 1,
          label: "快递至第三方卖家",
        },
      ],

      customStyle: {
        backgroundColor: this.$lightColor,
      },
      applyInfo: {},
      form: {
        orderItemSn: "", // 订单sn
        skuId: "",
        reason: "", //退款原因
        problemDesc: "", //退款说明
        images: [], //图片凭证
        num: 1, //退货数量
        goodsId: "", //商品id
        accountType: "",
        applyRefundPrice: "",
        refundWay: "",
        serviceType: "", //申请类型
        goodsStatus: '', // 货物状态
      },
      selectedReasonIndex: null, // 当前选中的原因索引
      goodsStatusList: [
        { value: 1, label: '未收到货' },
        { value: 2, label: '已收到货' }
      ],
      goodsStatusSelectShow: false,
      selectedGoodsStatusIndex: null,
      showLeaveModal: false, // 控制挽留弹窗
      flowPrice:''
    };
  },

  /**
   * 判断当前内容并生成数据
   */
  onLoad(options) {
    console.log(options);
    
    let navTitle = "申请售后";
    this.form.serviceType = "";
    if (options.value == 1) {
      navTitle = "申请退货退款";
      this.form.serviceType = "RETURN_GOODS";
    }
    if (options.value == 2) {
      navTitle = "申请换货";
      this.form.serviceType = "EXCHANGE_GOODS";
    }
    if (options.value == 3) {
      navTitle = "申请退款";
      this.form.serviceType = "RETURN_MONEY";
    }
    this.typeValue = options.value;
    uni.setNavigationBarTitle({
      title: navTitle, //此处写页面的title
    });
    this.sn = options.sn;
    this.sku = storage.getAfterSaleData();
    this.flowPrice = options.flowPrice;
    this.form.orderItemSn = options.sn;
    
    this.getReasonActions(this.form.serviceType);

    this.init(options.sn);
  },
  onBackPress(e) {
			if (e.from == "backbutton") {
				this.showLeaveModal = true;
				return true; // 阻止默认返回行为
			}
	},
  methods: {
    giveUpPay() {
				this.showLeaveModal = false;
        uni.navigateBack(0)
		},
    /** 获取申请原因下拉框数据 */
    async getReasonActions(serviceType) {
      uni.showLoading({
        title: "加载中",
      });
      await getAfterSaleReason(serviceType).then((res) => {
        if (res.data.success) {
          let action = [];
          res.data.result.forEach((item) => {
            action.push({
              value: item.id,
              label: item.reason,
            });
          });

          this.reasonList = action;
        }
      });
       if (this.$store.state.isShowToast){ uni.hideLoading() };
    },
    //打开地区选择器
    showCitySelect() {
      this.$refs.cityPicker.show();
    },

    // 初始化数据
    init(sn) {
      getAfterSaleInfo(sn).then((response) => {
        if (response.data.code == 400) {
          uni.showToast({
            title: response.data.message,
            duration: 2000,
            icon: "none",
          });
        } else {
          this.applyInfo = response.data.result;
          this.form.skuId = this.applyInfo.skuId;
          this.form.num = this.applyInfo.num;
          this.form.goodsId = this.applyInfo.goodsId;
          this.form.accountType = response.data.result.accountType;
        }
      });
    },

    //退款原因
    reasonSelectConfirm(e) {
      this.form.reason = e[0].label;
    },
    //退款方式
    typeSelectConfirm(e) {
      this.form.accountType = e[0].value;
      this.form.accountType_label = e[0].label;
    },
    //返回方式
    returnSelectConfirm(e) {},

    //修改申请数量
    valChange(e) {
      this.form.num = e.value;
    },
    //图片上传
    onUploaded(lists) {
      let images = [];

      lists.forEach((item) => {
        images.push(item.response.result);
      });
      this.form.images = images;
      // 通知上传完成
      this.$emit('upload-finish');
    },
    //提交申请
    async onSubmit() {
      //提交申请前检测参数
      if (!this.handleCheckParams()) {
        return;
      }
      try {
        // 检查是否有图片需要上传
        const hasImages = this.$refs.uUpload && this.$refs.uUpload.lists && this.$refs.uUpload.lists.length > 0;
        
        if (hasImages) {
          // 1. 先上传图片，等图片上传完
          await new Promise((resolve, reject) => {
            this.$refs.uUpload.upload();
            // 监听图片上传完成
            this.$once('upload-finish', resolve);
            // 可选：监听失败
            // this.$once('upload-fail', reject);
          });
        }
        
        // 2. 图片上传完（或没有图片），提交申请
        this.form.accountType = this.applyInfo.accountType;
        this.form.refundWay = this.applyInfo.refundWay;
        this.form.applyRefundPrice = this.applyInfo.applyRefundPrice;

        const resp = await applyReturn(this.sn, this.form);
        if (this.$store.state.isShowToast){ uni.hideLoading() };
        if (resp.data.success) {
          this.$refs.uToast.show({ title: "提交成功", type: "success" });
          uni.redirectTo({
            url: "/pages/order/afterSales/applySuccess",
          });
        } else {
          this.$refs.uToast.show({ title: resp.data.message, type: "error" });
        }
      } catch (e) {
        if (this.$store.state.isShowToast){ uni.hideLoading() };
        this.$refs.uToast.show({ title: "图片上传失败", type: "error" });
      }
    },
    //检测提交参数
    handleCheckParams() {
      // 退货退款时校验货物状态
      if (this.form.serviceType === 'RETURN_GOODS' && this.$u.test.isEmpty(this.form.goodsStatus)) {
        this.$refs.uToast.show({ title: "请选择 货物状态", type: "error" });
        return false;
      }
      if (this.$u.test.isEmpty(this.form.reason)) {
        this.$refs.uToast.show({ title: "请选择 退款原因", type: "error" });
        return false;
      }
      if (this.$u.test.isEmpty(this.form.problemDesc)) {
        this.$refs.uToast.show({ title: "请输入 退款说明", type: "error" });
        return false;
      }

      console.log(this.form.accountType)
      if (this.form.accountType == "BANK_TRANSFER") {
        // 银行开户行校验
        if (this.$u.test.isEmpty(this.form.bankDepositName)) {
          this.$refs.uToast.show({
            title: "请输入 银行开户行",
            type: "error",
          });
          return false;
        }
        // 银行开户名校验
        if (this.$u.test.isEmpty(this.form.bankAccountName)) {
          this.$refs.uToast.show({
            title: "请输入 银行开户名",
            type: "error",
          });
          return false;
        }
        // 银行账号校验
        if (this.$u.test.isEmpty(this.form.bankAccountNumber)) {
          this.$refs.uToast.show({
            title: "请输入 银行账号",
            type: "error",
          });
          return false;
        } else if (this.$u.test.chinese(this.form.bankAccountName) === false) {
          this.$refs.uToast.show({
            title: "银行开户名输入错误",
            type: "error",
          });
          return false;
        } else if (this.$u.test.chinese(this.form.bankDepositName) === false) {
          this.$refs.uToast.show({
            title: "银行开户行输入错误",
            type: "error",
          });
          return false;
        }
      }

      return true;
    },
    setType(type) {
      this.form.serviceType = type;
    },
    gotoFreightRule() {
      uni.navigateTo({
        url: '/pages/product/afterSalesPolicy?id=3',
      });
    },
    gotoPenaltyRule() {
      uni.navigateTo({
        url: '/pages/product/afterSalesPolicy?id=4',
      });
    },
    callPhone(phone) {
      uni.makePhoneCall({ phoneNumber: phone });
    },
    onShowReasonModal() {
      this.selectedReasonIndex = this.reasonList.findIndex(
        (item) => item.label === this.form.reason
      );
      this.reasonSelectShow = true;
      console.log('reasonList', this.reasonList, 'selectedReasonIndex', this.selectedReasonIndex);
    },
    selectReason(idx) {
      this.selectedReasonIndex = idx;
      this.form.reason = this.reasonList[idx].label;
      setTimeout(() => {
        this.reasonSelectShow = false;
      }, 200);
    },
    onShowGoodsStatusModal() {
      let idx = this.goodsStatusList.findIndex(
        (item) => item.label === this.form.goodsStatus
      );
      this.selectedGoodsStatusIndex = idx === -1 ? null : idx;
      this.goodsStatusSelectShow = true;
    },
    selectGoodsStatus(idx) {
      this.selectedGoodsStatusIndex = idx;
      this.form.goodsStatus = this.goodsStatusList[idx].label;
      setTimeout(() => {
        this.goodsStatusSelectShow = false;
      }, 200);
    },
    formatSpecs(specs) {
      if (!specs) return '无规格';
      // 如果是字符串，尝试解析
      if (typeof specs === 'string') {
        try {
          specs = JSON.parse(specs);
        } catch (e) {
          return '无规格';
        }
      }
      if (typeof specs !== 'object') return '无规格';
      const arr = Object.keys(specs)
        .filter(key => key !== 'images' && specs[key] !== undefined && specs[key] !== null && specs[key] !== '')
        .map(key => `${key}: ${specs[key]}`);
      return arr.length ? arr.join('；') : '无规格';
    },
  },
};
</script>

<style lang="scss" scoped>
.content {
  background: $page-color-base;
  // height: 100%;
  
}
.content {
  padding: 20rpx 32rpx 0 32rpx;

}
.body-view {
  
}
.after-sales-goods-detail-view {
  background-color: #fff;
  border-radius: 20rpx;
  .header {
    background-color: #f7f7f7;
    color: #999999;
    font-size: 22rpx;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    line-height: 70rpx;
    .header-text {
      background-color: #999999;
      padding: 10rpx 30rpx;
      border-radius: 50rpx;
    }
    .seller-name {
      color: $main-color;
    }
  }

  .goods-card {
    display: flex;
    align-items: center;
    background: #fff;
    border-radius: 12rpx;
    padding: 20rpx;
    // margin: 20rpx 0;
    border-radius: 20rpx;
    .goods-img {
      width: 80rpx;
      height: 80rpx;
      flex-shrink: 0;
      border-radius: 8rpx;
      overflow: hidden;
    }
    .goods-info {
      flex: 1;
      margin-left: 20rpx;
      .goods-title {
        width: 510rpx;
        font-size: 28rpx;
        color: #333;
        font-weight: 500;
        margin-bottom: 6rpx;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .goods-specs {
        font-size: 20rpx;
        color: #999999;
      }
    }
    .goods-num {
      color: #bbb;
      font-size: 22rpx;
      margin-left: 10rpx;
    }
  }
}
.after-num {
  margin: 0rpx 30rpx;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  height: 80rpx;
}

.opt-tip {
  margin-top: 20rpx;
  padding: 10rpx 30rpx;
  font-size: 22rpx;
}
.opt-view {
  background-color: #fff;
  margin-top: 20rpx;
  padding: 10rpx 30rpx;
  .how-view {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    height: 80rpx;
    border-bottom: 1px solid $page-color-base;
  }
  .explain-view {
    display: flex;
    flex-direction: row;
    align-items: center;
    height: 150rpx;
  }
  .img-title {
    height: 80rpx;
    display: flex;
    flex-direction: row;
    align-items: center;
  }
  .images-view {
    display: flex;
    flex-direction: row;
    align-items: center;
    flex-wrap: wrap;
  }
}
.item-apply-voucher {
  width: 100%;
  display: flex;
  justify-content: flex-end;
}
.submit-view {
  position: fixed;
  z-index: 1000;
  bottom: 0px;
  left: 0px;
  // border: solid 2rpx #f2f2f2;
  background-color: #ffffff;
  // height: 140rpx;
  width: 750rpx;
  justify-content: flex-end;
  padding: 20rpx;
  display: flex;
  align-items: center;
  padding-right: 32rpx;
  .submit-btn {
    width: 670rpx;
    height: 100rpx;
    background: linear-gradient( 90deg, #FF5235 0%, #FF9500 100%);
    border-radius: 86rpx 86rpx 86rpx 86rpx;
    font-weight: 400;
    font-size: 32rpx;
    color: #FFFFFF;
    text-align: center;
    line-height: 100rpx;
  }
}
.apply-type {
  display: flex;
  margin: 20rpx 0;
  .type-btn {
    flex: 1;
    text-align: center;
    padding: 20rpx 0;
    border-radius: 10rpx;
    background: #f5f5f5;
    color: #333;
    &.active {
      background: linear-gradient(90deg, #ff8800, #ff5000);
      color: #fff;
    }
  }
}
.apply-amount {
  background: #fff;
  margin: 20rpx 0;
  padding: 30rpx;
  border-radius: 20rpx;
  font-weight: 400;
  font-size: 28rpx;
  color: #333333;
  .amount {
    font-size: 48rpx;
    color: #ff5000;
    font-weight: bold;
  }
  .amount2 {
    font-weight: 600;
    font-size: 52rpx;
    color: #333333;
    border-bottom: 1rpx solid rgba(0,0,0,0.1);
    padding-bottom: 20rpx;
  }
  .desc {
    font-size: 22rpx;
    color: #999;
    margin-top: 32rpx;
  }
}
.img-upload {
  margin: 20rpx 0;
}


.refund-info-card {
  background: #fff;
  border-radius: 20rpx;
  margin: 20rpx 0 0 0;
  padding: 0 24rpx;
}
.refund-row {
  display: flex;
  align-items: center;
  height: 90rpx;
  border-bottom: 1px solid #f5f5f5;
  font-size: 28rpx;
  &:last-child {
    border-bottom: none;
  }
}
.refund-label {
  color: #333;
  flex: 1;
}
.refund-type-btn {
  font-weight: 400;
  font-size: 28rpx;
  color: #333333;
}
.refund-reason {
  display: flex;
  align-items: center;
  font-size: 28rpx;
}
.refund-reason-placeholder {
  color: #bbb;
}
.refund-penalty {
  color: #ff5000;
  font-size: 28rpx;
  font-weight: 500;
}
.rule-list {
  margin: 20rpx 0;
}
.rule-item {
  background: #fff;
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  padding: 32rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 30rpx;
  color: #333;
  cursor: pointer;
}

.apply-desc-card {
  background: #fff;
  border-radius: 20rpx;
  margin: 20rpx 0;
  padding: 32rpx;
  position: relative;
}
.desc-header {
  display: flex;
  justify-content: flex-end;
}
.desc-limit {
  color: #999;
  font-size: 24rpx;
}
.desc-label {
  color: #bbb;
  font-size: 30rpx;
  margin: 24rpx 0 16rpx 0;
}
.desc-textarea {
  width: 95%;
  min-height: 100rpx;
  background: #FFF;
  border-radius: 12rpx;
  padding: 20rpx;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 24rpx;
  border: none;
  resize: none;
}
.desc-upload {
  margin-top: 0;
}
.upload-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 160rpx;
  height: 160rpx;
  background: #F7F7F7;
  border-radius: 12rpx;
  image {
    width: 59rpx;
    height: 51rpx;
  }
}
.upload-text {
  font-weight: 400;
  font-size: 20rpx;
  color: #666666;
  margin-top: 12rpx;
}

.contact-card {
  background: #fff;
  border-radius: 20rpx;
  padding: 32rpx;
  margin: 20rpx 0;
  display: flex;
  align-items: center;
  font-size: 30rpx;
  color: #333;
}
.contact-label {
  color: #999;
  flex: none;
  margin-right: 32rpx;
}
.contact-value {
  color: #333;
  font-weight: 500;
  flex: 1;
  text-align: left;
}
.reason-modal-mask {
  position: fixed;
  z-index: 9999;
  left: 0; top: 0; right: 0; bottom: 0;
  background: rgba(0,0,0,0.4);
  display: flex;
  align-items: flex-end;
  justify-content: center;
}
.reason-modal {
  width: 100vw;
  max-width: 100vw;
  background: #fff;
  border-radius: 24rpx 24rpx 0 0;
  padding-bottom: 32rpx;
  animation: modalIn .2s;
}
@keyframes modalIn {
  from { transform: translateY(100%);}
  to { transform: translateY(0);}
}
.reason-modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 32rpx 0 32rpx;
  position: relative;
  text-align: center;
}
.close_btn {
  position: absolute;
  top: 40rpx;
  right: 40rpx;
}
.reason-modal-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  text-align: center;
  flex: 1;
}
.reason-modal-list {
  max-height: 600rpx;
  overflow-y: auto;
  margin: 24rpx 0 0 0;
}
.reason-modal-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 28rpx 32rpx;
  font-size: 30rpx;
  color: #333;
  // border-bottom: 1px solid #f5f5f5;
  cursor: pointer;
  background: #fff;
  &.selected {
    color: #ff5000;
    background: #fff7f0;
    font-weight: 500;
  }
}
.reason-modal-label {
  flex: 1;
}
.reason-modal-radio {
  margin-left: 24rpx;
  image {
    width: 32rpx;
    height: 32rpx;
  }
  view {
    width: 32rpx;
    height: 32rpx;
    border-radius: 50%;
    border: 1rpx solid #999999;
  }
}
.radio-outer {
  width: 36rpx;
  height: 36rpx;
  border-radius: 50%;
  border: 2rpx solid #ff5000;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fff;
}
.radio-check {
  font-size: 28rpx;
  color: #ff5000;
  font-weight: bold;
  line-height: 1;
}

.leave-modal-mask {
  position: fixed;
  left: 0; top: 0; right: 0; bottom: 0;
  background: rgba(0,0,0,0.45);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
}
.leave-modal-box {
  background: #fff;
  border-radius: 18rpx;
  width: 520rpx;
  padding: 48rpx 0 0 0;
  box-shadow: 0 8rpx 32rpx 0 rgba(0,0,0,0.10);
  text-align: center;
}
.leave-modal-title {
  font-size: 32rpx;
  color: #222;
  font-weight: 500;
  margin-bottom: 18rpx;
}
.leave-modal-desc {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 40rpx;
  padding: 0 40rpx;
}
.leave-modal-btns {
  display: flex;
  border-top: 1rpx solid #eee;
  height: 96rpx;
  margin-top: 0;
}
.leave-modal-btn {
  flex: 1;
  font-size: 32rpx;
  line-height: 96rpx;
  cursor: pointer;
}
.leave-modal-btn.cancel {
  color: #999;
  border-right: 1rpx solid #eee;
}
.leave-modal-btn.confirm {
  color: #FF5134;
  font-weight: 500;
}
</style>
