
<template>
  <div class="layout">
    <div class="" @click="modelNavigateTo(res.list[0])">
      <u-image width="300rpx" height="436rpx" class="image-mode" :src="res.list[0].img">
        <u-loading slot="loading"></u-loading>
      </u-image>
    </div>
    <div class=" right">
      <div class="view-height-75" @click="modelNavigateTo(res.list[1])">
        <u-image width="366rpx" height="208rpx" class="image-mode" :src="res.list[1].img" alt>
          <u-loading slot="loading"></u-loading>
        </u-image>
      </div>
      <div class="view-height-75" @click="modelNavigateTo(res.list[2])">
        <u-image width="366rpx" height="208rpx" class="image-mode" :src="res.list[2].img" alt>
          <u-loading slot="loading"></u-loading>
        </u-image>
      </div>
    </div>
  </div>
</template>
<script>

import { modelNavigateTo } from "./tpl";
export default {
  title: "左一右二",
  props: ["res"],
  data() {
    return {
      modelNavigateTo,
    };
  },
  mounted() {},
};
</script>
<style lang="scss" scoped>
@import "./tpl.scss";
.layout {
  display: flex;
  align-items: center;
  justify-content: space-around;
  background-size: cover;
  margin: 0 !important;
}
.right {
  width: 366rpx;
  height: 436rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  margin-left: 20rpx;
}
</style>