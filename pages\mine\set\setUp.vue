<template>
  <view class="container">
    <view class="cell-group" v-if="userInfo.id">
      <u-cell-item
        title="账户安全"
        :border-bottom="false"
        @click="navigateTo('/pages/mine/set/securityCenter/securityCenter')"
      ></u-cell-item>
     
    </view>
    <view class="cell-group">
      <u-cell-item title="隐私协议" @click="navigateTo('/pages/mine/set/privacy')"></u-cell-item>
      <u-cell-item v-if="userInfo.id" title="收货地址" @click="navigateTo('/pages/mine/address/address')"></u-cell-item>
      <u-cell-item title="关于我们" @click="navigateTo('/pages/mine/set/editionIntro')"></u-cell-item>
      <u-cell-item title="售后政策" :border-bottom="false" @click="navigateTo(`/pages/product/afterSalesPolicy?id=2`)"></u-cell-item>
    </view>
    <view v-if="userInfo.id" class="safe-logout-btn" @click="quiteLoginOut">安全退出</view>
  </view>
</template>

<script>
import config from "@/config/config";
export default {
  data() {
    return {
      config,
      userImage: config.defaultUserPhoto,
      isCertificate: false,
      userInfo: {},
      fileSizeString: "0B",
    };
  },
  methods: {
    goBack() {
      uni.navigateBack();
    },
    navigateTo(url) {
      uni.navigateTo({ url });
    },
    quiteLoginOut() {
      this.$options.filters.quiteLoginOut(this);
    },
  },
  onShow() {
    this.userInfo = this.$options.filters.isLogin();
  },
};
</script>

<style lang='scss' scoped>
.container {
  background: #fafafa;
  display: flex;
  flex-direction: column;
}
.navbar {
  display: flex;
  align-items: center;
  height: 100rpx;
  background: #fff;
  padding: 0 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}
.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 36rpx;
  font-weight: bold;
}
.cell-group {
  margin: 30rpx 20rpx 0 20rpx;
  border-radius: 16rpx;
  background: #fff;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.03);
}
.safe-logout-btn {
  width: 670rpx;
  height: 100rpx;
  border-radius: 78rpx 78rpx 78rpx 78rpx;
  line-height: 100rpx;
  text-align: center;
  color: #fff;
  font-size: 32rpx;
  border-radius: 45rpx;
  background: linear-gradient( 90deg, #FF5235 0%, #FF9500 100%);
  box-shadow: 0 4rpx 16rpx rgba(255, 124, 52, 0.15);
  position: fixed;
  bottom: 50rpx;
  left: 50%;
  transform: translateX(-50%);
}
</style>
