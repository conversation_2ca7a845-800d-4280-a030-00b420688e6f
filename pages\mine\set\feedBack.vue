<template>
  <div class="feedBack">
    <div class="feedBack-box">
      <view class="category-box">
        <view class="category-title">
          <text class="required">*</text> 选择分类
        </view>
        <view class="category-list">
          <view
            class="category-item"
            :class="{ active: feedBack.type == item.value }"
            v-for="(item, index) in list"
            :key="index"
            @click="handleClick(index)"
          >
            {{ item.text }}
          </view>
        </view>
      </view>
    </div>

    <div class="feedBack-box">
      <h4>
        <text class="required">*</text> 问题反馈
        <span style="margin-left: 10rpx" v-if="feedBack.type"
          >@{{
            list.find((item) => {
              return item.value == feedBack.type;
            }).text
          }}</span
        >
      </h4>
      <view class="textarea-wrap">
        <textarea
          v-model="feedBack.context"
          class="custom-textarea"
          :maxlength="200"
          placeholder="请留下您的宝贵意见（最少输入4个字）"
          placeholder-class="textarea-placeholder"
          @input="onInput"
        />
        <view class="textarea-count"
          >还可以输入{{
            200 - (feedBack.context ? feedBack.context.length : 0)
          }}字</view
        >
      </view>
    </div>

    <!-- 上传凭证 -->
    <div class="feedBack-box">
      <view class="opt-view">
        <view class="img-title">上传凭证（最多5张）</view>
        <view class="images-view">
          <u-upload
            ref="uUpload"
            :header="{ accessToken: storage.getAccessToken() }"
            :action="action"
            width="150"
            @on-uploaded="onUploaded"
            :max-count="5"
            :show-progress="false"
            :custom-btn="true"
             :auto-upload="false"
          >
            <view slot="addBtn" class="upload-btn">
              <image src="/static/xj.png" mode="scaleToFill" />
              <view class="upload-text">图片</view>
            </view>
          </u-upload>
        </view>
      </view>
    </div>

    <div class="feedBack-box phone-box">
      <view class="phone-row">
        <text class="phone-label">联系电话</text>
        <input
          class="phone-input"
          v-model="feedBack.mobile"
          type="number"
          maxlength="11"
          placeholder="请填写可以联系到您的手机号"
          placeholder-class="phone-placeholder"
        />
      </view>
    </div>

    <div class="submit" @click="submit()">确认提交</div>
  </div>
</template>

<script>
import storage from "@/utils/storage.js";
import config from "@/config/config";
import { feedBack } from "@/api/members.js";
import { upload } from "@/api/common.js";
export default {
  data() {
    return {
      storage,
      config,
      feedBack: {
        type: "FUNCTION", //默认反馈问题为 '功能相关'
      },
      action: upload, //图片上传地址
      list: [
        { text: "功能相关", value: "FUNCTION" },
        { text: "优化反馈", value: "OPTIMIZE" },
        { text: "其他", value: "OTHER" },
      ],
    };
  },
  methods: {
    // 点击反馈内容
    handleClick(index) {
      this.$set(this.feedBack, "type", this.list[index].value);
    },
    onInput(e) {
      // 兼容部分平台 e.detail.value
      this.feedBack.context =
        e.detail && e.detail.value !== undefined
          ? e.detail.value
          : e.target.value;
    },
    //图片上传
    onUploaded(lists) {
      let images = [];
      lists.forEach((item) => {
        images.push(item.response.result);
      });
      this.feedBack.images = images.join(",");
      // 如果图片数量达到最大，或者全部上传完毕，手动触发 upload-finish
      if (!this.$refs.uUpload.lists.some(item => item.progress !== 100)) {
        this.$emit('upload-finish');
      }
    },

    /**
     * 提交意见反馈
     */
    async submit() {
      if (!this.feedBack.type) {
        uni.showToast({
          title: "请填写反馈类型",
          duration: 2000,
          icon: "none",
        });
        return false;
      }
      if (!this.feedBack.context) {
        uni.showToast({
          title: "请填写反馈信息",
          duration: 2000,
          icon: "none",
        });
        return false;
      }
      if (this.feedBack.context && this.feedBack.context.length < 4) {
        uni.showToast({
          title: "反馈内容至少4个字",
          duration: 2000,
          icon: "none",
        });
        return false;
      }
      if (this.feedBack.mobile && !this.$u.test.mobile(this.feedBack.mobile)) {
        uni.showToast({
          title: "请填写您的正确手机号",
          duration: 2000,
          icon: "none",
        });
        return false;
      }
      // 检查是否有图片需要上传
      const hasImages = this.$refs.uUpload && this.$refs.uUpload.lists && this.$refs.uUpload.lists.length > 0;
      if (hasImages) {
        await new Promise((resolve, reject) => {
          this.$refs.uUpload.upload();
          this.$once('upload-finish', resolve);
          // 可选：监听失败
          // this.$once('upload-fail', reject);
        });
      }
      // 图片上传完毕后提交
      feedBack(this.feedBack).then((res) => {
        if (res.data.success) {
          uni.showToast({
            title: "提交成功!",
            duration: 2000,
            icon: "none",
          });
          setTimeout(() => {
            uni.navigateBack({
              delta: 1,
            });
          }, 500);
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.submit {
  text-align: center;
  width: 670rpx;
  height: 100rpx;
  background: linear-gradient( 90deg, #FF5235 0%, #FF9500 100%);
  border-radius: 86rpx 86rpx 86rpx 86rpx;
  margin-bottom: 100rpx;
  line-height: 100rpx;
  margin: 0 auto;
  border-radius: 100px;
  font-weight: 400;
  font-size: 32rpx;
  color: #FFFFFF;
}
.active {
  color: $light-color !important;
  font-weight: bold;
}
.feedBack {
  padding-bottom: 100rpx;
  padding: 20rpx 32rpx;
}
.feedBack-box {
  background: #fff;
  border-radius: 20rpx;
  padding: 32rpx;
  margin-bottom: 20rpx;
}
/deep/ .u-input__textarea {
  padding: 12px;
}

.feedBack-item {
  font-size: 24rpx;
  color: #666;
}
h4 {
  font-size: 30rpx;
}
.field-input {
  margin: 20rpx 0;
  padding: 20rpx 0;
  background: #fafafa;
  border-radius: 0.6em;
}
.category-box {
  background: #fff;
  border-radius: 16rpx;
}
.category-title {
  font-size: 28rpx;
  font-weight: bold;
  margin-bottom: 24rpx;
}
.required {
  color: #ff5134;
  margin-right: 8rpx;
}
.category-list {
  display: flex;
  flex-wrap: wrap;
  gap: 24rpx 32rpx;
}
.category-item {
  min-width: 160rpx;
  text-align: center;
  background: #f6f6f6;
  border-radius: 118rpx;
  padding: 20rpx 36rpx;
  font-size: 28rpx;
  color: #222;
  margin-bottom: 16rpx;
  transition: all 0.2s;
  border: 1rpx solid #f6f6f6;
}
.category-item.active {
  background: #ffede6;
  color: #ff5134;
  font-weight: bold;
  border: 1rpx solid #ff5134;
}
.textarea-wrap {
  position: relative;
  background: #f7f7f7;
  border-radius: 20rpx;
  min-height: 220rpx;
  padding: 32rpx 32rpx 56rpx 32rpx;
  box-sizing: border-box;
  margin-top: 20rpx;
}
.custom-textarea {
  width: 100%;
  min-height: 140rpx;
  font-size: 28rpx;
  color: #333;
  background: transparent;
  border: none;
  outline: none;
  resize: none;
}
.textarea-placeholder {
  color: #cccccc;
  font-size: 28rpx;
}
.textarea-count {
  position: absolute;
  right: 32rpx;
  bottom: 20rpx;
  color: #cccccc;
  font-size: 24rpx;
}
.img-title {
  font-weight: 400;
  font-size: 28rpx;
  color: #000000;
  margin-bottom: 20rpx;
}
.upload-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 160rpx;
  height: 160rpx;
  background: #F7F7F7;
  border-radius: 12rpx;
  
  image {
    width: 68rpx;
    height: 68rpx;
  }
  .upload-text {
    font-weight: 400;
    font-size: 20rpx;
    color: #666666;
  }
}
.phone-box {
  padding: 0 32rpx;
  height: 98rpx;
  display: flex;
  align-items: center;
  margin-bottom: 40rpx;
}
.phone-row {
  display: flex;
  align-items: center;
  width: 100%;
}
.phone-label {
  font-weight: 400;
  font-size: 24rpx;
  color: #000000;
  margin-right: 40rpx;
}
.phone-input {
  flex: 1;
  border: none;
  outline: none;
  background: transparent;
  font-size: 28rpx;
  color: #222;
  height: 80rpx;
}
.phone-placeholder {
  font-weight: 400;
  font-size: 24rpx;
  color: #999999;
}
</style>
