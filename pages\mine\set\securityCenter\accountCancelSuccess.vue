<template>
  <view class="container">
    <view class="success-box">
      <image
        class="success-icon"
        src="/static/img/success.png"
        mode="aspectFit"
      ></image>
      <view class="success-title">账号已成功注销</view>
      <view class="success-desc">感谢您的使用，期待再次相遇！</view>
      
    </view>
    <view class="back-btn" @click="goHome">完成</view>
  </view>
</template>

<script>
export default {
  methods: {
    goHome() {
      uni.reLaunch({ url: "/pages/tabbar/home/<USER>" });
    },
  },
};
</script>

<style lang="scss" scoped>
.container {
  background: #fafafa;
  overflow: hidden;
}
.success-box {
  margin: 20rpx 32rpx 0 32rpx;
  background: #fff;
  border-radius: 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60rpx 20rpx 40rpx 20rpx;
}
.success-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 32rpx;
}
.success-title {
  font-size: 36rpx;
  color: #222;
  font-weight: 500;
  margin-bottom: 16rpx;
}
.success-desc {
  font-size: 28rpx;
  color: #888;
  // margin-bottom: 60rpx;
}
.back-btn {
  width: 670rpx;
  height: 100rpx;
  background: linear-gradient( 90deg, #FF5235 0%, #FF9500 100%);
  color: #fff;
  font-size: 32rpx;
  border-radius: 50rpx;
  border: none;
  font-weight: 400;
  text-align: center;
  line-height: 100rpx;
  margin: 40rpx auto 0;
}

</style>
