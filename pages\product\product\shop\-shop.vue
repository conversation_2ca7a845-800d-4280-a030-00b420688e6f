<template>
  <view v-if="storeDetail">
    <!-- 商店信息  -->
    <view class="store-info" @click="tostorePage(storeDetail)">
      <view class="logo-wrapper">
        <u-image width="96rpx" height="96rpx" :src="storeDetail.storeLogo" border-radius="50%" mode="aspectFill"></u-image>
      </view>
      <view class="store-details">
        <view class="store-name">{{ storeDetail.storeName }}</view>
        <view class="tags-row">
          <text class="tag-item fast-delivery">快速发货</text>
          <text class="tag-item fast-quality">正品保证</text>
          <text class="tag-item brand-selected">品牌精选</text>
        </view>
      </view>
      <view class="arrow-icon">
        <u-icon name="arrow-right" color="#999999" size="26"></u-icon>
      </view>
    </view>

    <!-- <view class="store-recommend">
      <view class="store-recommend-title">店内其他商品</view>
      <view class="recommend-list">
        <view class="recommend-item" @click="clickGoods(item)" v-for="(item, index) in res" :key="index">
          <u-image class="recommend-item-img" :fade="true" duration="450" :lazy-load="true" :src="item.thumbnail" height="218rpx">
            <u-loading slot="loading"></u-loading>
            <view slot="error" style="font-size: 24rpx; ">加载失败</view>
          </u-image>
          <view class="recommend-item-name">
            {{ item.goodsName }}
          </view>
          <view class="item-price" v-if="item.price != undefined">
            ￥<span class="item-price-blod">{{ $options.filters.goodsFormatPrice(item.price)[0] }}</span>.{{ $options.filters.goodsFormatPrice(item.price)[1] }}
          </view>
        </view>
      </view>
    </view> -->
  </view>
</template>

<script>
export default {
  data() {
    return {};
  },
  props: ["res", "goodsDetail", "storeDetail"],
  mounted() {},
  methods: {
    // 点击商品
    clickGoods(val) {
      uni.navigateTo({
        url: `/pages/product/goods?id=${val.id}&goodsId=${val.id}`,
      });
    },

    tostorePage(val) {
      uni.navigateTo({
        // url: "../product/shopPage?id=" + val.storeId,
        url: "../product/shopPageDetail?id=" + val.storeId,
      });
    },
  },
};
</script>

<style lang="scss" scoped>
@import "../product.scss";
.recommend-item-name {
  height: 60rpx;
  color: #333;
  font-weight: 400;
  font-size: 24rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
.item-price-blod {
  font-weight: bold;
  font-size: 32rpx;
}
.recommend-item {
  width: 30%;
  margin: 10rpx 0rpx;
  overflow: hidden;
  border-radius: 12rpx;

  /deep/ .u-image__image {
    height: 218rpx;
    border-radius: 12rpx !important;
  }
}

.recommend-item-img {
  /deep/ .u-image__image {
    width: 100% !important;
  }
}

.recommend-list-view {
  width: 100%;
}
.shopTag {
  background: $main-color;
  font-size: 24rpx;
  margin-left: 10rpx;
  padding: 6rpx 12rpx;
  border-radius: 10rpx;
  font-weight: normal;
  color: #fff;
}

.store-info {
  display: flex;
  align-items: center;
  background: #fff;
  padding: 30rpx;
  margin: 20rpx 32rpx;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);

  .logo-wrapper {
    margin-right: 20rpx;
  }

  .store-details {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }

  .store-name {
    font-size: 32rpx;
    font-weight: 500;
    color: #333;
    margin-bottom: 10rpx;
  }

  .tags-row {
    display: flex;
    align-items: center;

    .tag-item {
      font-size: 20rpx;
      padding: 6rpx 12rpx;
      border-radius: 8rpx;
      margin-right: 10rpx;
      color: #fff;
      line-height: 1;

      &.fast-delivery {
        background: linear-gradient( 90deg, #FF5235 0%, #FF9500 100%);
        color: #fff;
      }
      &.fast-quality {
        background: #21214A;
        color: #FFE9C0;
      }
      &.brand-selected {
        background: linear-gradient( 90deg, #AC8E6F 0%, #E6C186 100%);
        color: #FFE9B8;
      }
    }
  }

  .arrow-icon {
    margin-left: 20rpx;
  }
}

/* star */
.star-con {
  display: flex;
  flex-direction: column;

  view {
    display: flex;
    align-items: center;

    .star {
      width: 30rpx;
      height: 30rpx;
      background: url(https://image.shutterstock.com/image-vector/star-icon-vector-illustration-260nw-474687040.jpg);
      background-size: 100%;
    }
  }
}

.recommend-list {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;

  /deep/ .u-row {
    width: 100%;
  }

  .item-price {
    margin: 10rpx 0 20rpx 0;
    text-align: left;
    font-weight: bold;
    overflow: hidden;
    white-space: nowrap;
    margin: 18rpx 0;
    font-size: 22rpx;
    color: $price-color;
  }

  .recommend-list-con {
    display: flex;
    flex-direction: column;
    width: 32%;
    margin-bottom: 24rpx;
  }

  .name {
    overflow: hidden;
    white-space: nowrap;
  }
}

.store-recommend {
  background: #fff;

  margin: 20rpx 0 0 0;
}
.goodsNum,
.collectionNum {
  font-size: 24rpx;
  color: #999;
}
.store-row {
  display: flex;
  margin: 10rpx 0;
  > div {
    margin-right: 20rpx;
  }
}
</style> 