<template>
  <view class="id-auth-page">
    <view v-if="loading" class="loading-container">
      <u-loading text="加载中..." mode="flower" size="58"/>
    </view>
    <view v-else>
      <view v-if="step === 0">
        <!-- 标题和额度提示 -->
        <view class="title-bar_box">
          <view class="title-bar">
            <text class="main-title">上传本人身份证</text>
            <text class="quota"
              >预计可借款<text class="quota-num">20万</text>额度</text
            >
          </view>
          <view class="desc">
            <image src="/static/financial/desc.png" mode="scaleToFill" />
            用于身份安全核验、反洗钱链路评估，资料将被加密保护
          </view>
          <!-- 步骤条 -->
          <view class="custom-steps-bar">
            <!-- 标题 -->
            <view class="steps-labels">
              <view
                class="step-label"
                v-for="(item, idx) in steps"
                :key="item.name"
                :class="{
                  left: idx === 0,
                  right: idx === steps.length - 1,
                }"
              >
                {{ item.name }}
              </view>
            </view>
            <!-- 步进器 -->
            <view class="steps-track">
              <template v-for="(item, idx) in steps">
                <view class="step-icon" :key="'icon' + idx">
                  <!-- 已完成 -->
                  <view class="step-done" v-if="idx < step">
                    <image
                      src="/static/financial/step_Act.png"
                      mode="scaleToFill"
                      class="step-icon-img"
                    />
                  </view>
                  <!-- 当前步骤（等待/激活） -->
                  <view class="step-active" v-else-if="idx === step">
                    <view class="step-yuan"></view>
                  </view>
                  <!-- 未激活 -->
                  <view v-else class="step-noActive"></view>
                </view>
                <view
                  v-if="idx < steps.length - 1"
                  class="step-line"
                  :class="{ active: idx < step }"
                  :key="'line' + idx"
                ></view>
              </template>
            </view>
          </view>
        </view>

        <!-- 上传身份证 -->
        <view class="upload-section">
          <view class="upload-item">
            <view v-show="frontShow">
              <u-upload
                ref="frontUpload"
                :header="{ accessToken: storage.getAccessToken() }"
                :action="action"
                :file-list="frontList"
                :max-count="1"
                width="304"
                height="196"
                :form-data="{directoryPath:'idCard'}"
                :show-tips="false"
                :custom-btn="true"
                @afterRead="onFrontRead"
                @on-remove="onFrontRemove"
                @on-uploaded="onFrontUploaded"
                @on-error="onFrontUploadError"
                @on-success="onFrontUploadSuccess"
                :show-progress="false"
              >
                <view slot="addBtn" class="upload-placeholder">
                  <image
                    src="/static/financial/idcard-front-bg.png"
                    class="idcard-bg"
                    mode="aspectFill"
                  />
                </view>
              </u-upload>
            </view>
            <view v-show="!frontShow" class="upload-encrypt">
              <image
                src="/static/financial/idcard-front-encrypt.png"
                mode="scaleToFill"
              />
              <view class="upload-encrypt-det" @tap="frontShow = true"></view>
            </view>
          </view>
          <view class="upload-item">
            <view v-show="backShow">
              <u-upload
                ref="backUpload"
                :header="{ accessToken: storage.getAccessToken() }"
                :action="action"
                :file-list="backList"
                :form-data="{directoryPath:'idCard'}"
                :max-count="1"
                width="304"
                height="196"
                :show-tips="false"
                :custom-btn="true"
                @afterRead="onBackRead"
                @on-remove="onBackRemove"
                @on-uploaded="onBackUploaded"
                @on-error="onBackUploadError"
                 @on-success="onBackUploadSuccess"
                :show-progress="false"
              >
                <view slot="addBtn" class="upload-placeholder">
                  <image
                    src="/static/financial/idcard-back-bg.png"
                    class="idcard-bg"
                    mode="aspectFill"
                  />
                </view>
              </u-upload>
            </view>
            <view v-show="!backShow" class="upload-encrypt">
              <image
                src="/static/financial/idcard-back-encrypt.png"
                mode="scaleToFill"
              />
              <view class="upload-encrypt-det" @tap="backShow = true"></view>
            </view>
          </view>
        </view>

        <!-- 上传须知 -->
        <view class="notice">
          <view v-if="!idInfoShow" class="notice-card">
            <image src="/static/financial/card.png" mode="scaleToFill" />
          </view>
          <view v-else class="id-info-card">
            <view class="id-info-title">请核对一下信息，如有误请更改</view>
            <view class="id-info-row-box">
              <view class="id-info-row">
                <text class="id-info-label">真实姓名</text>
                <u-input
                  v-model="idInfo.name"
                  :border="false"
                  class="id-info-input"
                  @blur="validateRealName"
                />
                <u-icon
                  name="edit-pen"
                  size="32"
                  color="#bbb"
                  class="id-info-edit"
                />
              </view>
              <view class="id-info-row">
                <text class="id-info-label">身份证号</text>
                <u-input
                  v-model="idInfo.id"
                  :border="false"
                  class="id-info-input"
                  :disabled="true"
                />
                <!-- <u-icon
                  name="edit-pen"
                  size="32"
                  color="#bbb"
                  class="id-info-edit"
                /> -->
              </view>
              <!-- <view class="id-info-row">
            <text class="id-info-label">签发机关</text>
            <u-input
              v-model="idInfo.org"
              :disabled="!idInfoEdit.org"
              border="none"
              class="id-info-input"
            />
            <u-icon
              name="edit-pen"
              size="22"
              color="#bbb"
              class="id-info-edit"
              @click="idInfoEdit.org = !idInfoEdit.org"
            />
          </view>
          <view class="id-info-row">
            <text class="id-info-label">有效期</text>
            <u-input
              v-model="idInfo.date"
              :disabled="!idInfoEdit.date"
              border="none"
              class="id-info-input"
            />
            <u-icon
              name="edit-pen"
              size="22"
              color="#bbb"
              class="id-info-edit"
              @click="idInfoEdit.date = !idInfoEdit.date"
            />
          </view> -->
            </view>
          </view>
        </view>
        <view class="info-bottomIdAuth">
         <!-- 协议勾选 -->
          <view class="agreement flex-a-c">
            <u-checkbox v-model="checked" shape="circle" size="28" active-color="#ff6b35" />
            <text
              >我已阅读并同意
              <text class="link" @click="openAgreement">《平台服务协议》</text
              >等重要协议</text
            >
          </view>

          <!-- 提交按钮 -->
          <u-button :custom-style="btnStyle" @click="submit" class="submit-btn">{{
            btnText
          }}</u-button>
          <view class="info-safe-tip">
            <image src="/static/financial/desc.png" mode="scaleToFill" />
            平台承诺保护您的信息安全
          </view>
        </view>
        
      </view>

      <view v-if="step === 1" style="background: #fff">
        <!-- 步骤条 -->
        <view class="custom-steps-bar1">
          <!-- 标题 -->
          <view class="steps-labels">
            <view
              class="step-label"
              v-for="(item, idx) in steps"
              :key="item.name"
              :class="{
                left: idx === 0,
                right: idx === steps.length - 1,
              }"
            >
              {{ item.name }}
            </view>
          </view>
          <!-- 步进器 -->
          <view class="steps-track">
            <template v-for="(item, idx) in steps">
              <view class="step-icon" :key="'icon' + idx">
                <!-- 已完成 -->
                <view class="step-done" v-if="idx < step">
                  <image
                    src="/static/financial/step_Act.png"
                    mode="scaleToFill"
                    class="step-icon-img"
                  />
                </view>
                <!-- 当前步骤（等待/激活） -->
                <view class="step-active" v-else-if="idx === step">
                  <view class="step-yuan"></view>
                </view>
                <!-- 未激活 -->
                <view v-else class="step-noActive"></view>
              </view>
              <view
                v-if="idx < steps.length - 1"
                class="step-line"
                :class="{ active: idx < step }"
                :key="'line' + idx"
              ></view>
            </template>
          </view>
        </view>
        <view class="info-title-box">
          <view class="info-title">完善信息激活额度</view>
          <view class="info-desc">补全以下信息，授权通讯信息用于额度评估</view>
        </view>
        <view class="info-section">
          <view class="info-group-title">基本信息</view>
          <view class="info-cell" @click="openPicker('education')">
            <text class="info-label">学历</text>
            <view class="info-cell-flex">
              <text
                class="info-value"
                :class="{ selected: infoForm.education }"
                >{{ infoForm.education || "请选择您的学历" }}</text
              >
              <u-icon name="arrow-right" size="22" color="#bbb" />
            </view>
          </view>
          <view class="info-cell" @click="openPicker('marriage')">
            <text class="info-label">婚姻状况</text>
            <view class="info-cell-flex">
              <text class="info-value" :class="{ selected: infoForm.marriage }">{{
                infoForm.marriage || "请选择婚姻状况"
              }}</text>
              <u-icon name="arrow-right" size="22" color="#bbb" />
            </view>
          </view>
          <view class="info-cell" @click="showCityPicker">
            <text class="info-label">居住城市</text>
            <view class="info-cell-flex">
              <text class="info-value" :class="{ selected: cityPath }">{{
                cityPath || "请选择居住城市"
              }}</text>
              <u-icon name="arrow-right" size="22" color="#bbb" />
            </view>
          </view>
          <view class="info-cell">
            <text class="info-label">详细地址</text>
            
            <u-input
              type="textarea"
              maxlength="100" 
              minlength="7"
              height="0" 
              v-model="infoForm.address"
              placeholder="请输入详细地址，具体到门牌号"
              :border="false"
              class="info-value"
              :class="{ selected: infoForm.address }"
              input-align="right"
              :placeholder-style="placeholderStyle"
            />
          </view>
          <view class="info-cell" @click="openPicker('income')">
            <text class="info-label">月收入</text>
            <view class="info-cell-flex">
              <text class="info-value" :class="{ selected: infoForm.income }">{{
                infoForm.income || "请选择月收入"
              }}</text>
              <u-icon name="arrow-right" size="22" color="#bbb" />
            </view>
          </view>
        </view>
        <view class="info-section">
          <view class="info-group-title">工作信息</view>

          <view class="info-cell" @click="openPicker('industry')">
            <text class="info-label">所属行业</text>
            <view class="info-cell-flex">
              <text class="info-value" :class="{ selected: infoForm.industry }">{{
                infoForm.industry || "请选择所属行业"
              }}</text>
              <u-icon name="arrow-right" size="22" color="#bbb" />
            </view>
          </view>
          <view class="info-cell" @click="openPicker('job')">
            <text class="info-label">职业</text>
            <view class="info-cell-flex">
              <text class="info-value" :class="{ selected: infoForm.job }">{{
                infoForm.job || "请选择您的职业"
              }}</text>
              <u-icon name="arrow-right" size="22" color="#bbb" />
            </view>
          </view>
        </view>

        <!-- 紧急联系人模块 -->
        <view class="info-section">
          <view class="info-group-title">紧急联系人</view>
          <view class="info-cell" @click="openPicker('emergencyRelation')">
            <text class="info-label">关系</text>
            <view class="info-cell-flex">
              <text
                class="info-value"
                :class="{ selected: infoForm.emergencyRelation }"
                >{{ infoForm.emergencyRelation || "请选择" }}</text
              >
              <u-icon name="arrow-right" size="22" color="#bbb" />
            </view>
          </view>
          <view class="info-cell">
            <text class="info-label">姓名</text>
            <u-input
              v-model="infoForm.emergencyName"
              placeholder="请输入真实姓名"
              :border="false"
              class="info-value"
              input-align="right"
              :placeholder-style="placeholderStyle"
              @blur="validateEmergencyName(1)"
            />
          </view>
          <view class="info-cell">
            <text class="info-label" style="width: 120rpx; flex-shrink: 0"
              >手机号</text
            >
            <view class="info-cell-flex" style="flex: 1; align-items: center">
              <u-input
                v-model="infoForm.emergencyPhone"
                placeholder="请输入联系人手机号"
                :border="false"
                class="info-value"
                input-align="right"
                :placeholder-style="placeholderStyle"
                style="width: 340rpx"
                type="number"
                maxlength="11"
              />
              <!-- <image
                src="/static/financial/contact.png"
                style="width: 44rpx; height: 44rpx; margin-left: 8rpx"
                @click="chooseContact(1)"
              /> -->
            </view>
          </view>
        </view>
        <!-- 紧急联系人2模块 -->
        <view class="info-section">
          <view class="info-group-title">紧急联系人</view>
          <view class="info-cell" @click="openPicker('emergencyRelation2')">
            <text class="info-label">关系</text>
            <view class="info-cell-flex">
              <text
                class="info-value"
                :class="{ selected: infoForm.emergencyRelation2 }"
                >{{ infoForm.emergencyRelation2 || "请选择" }}</text
              >
              <u-icon name="arrow-right" size="22" color="#bbb" />
            </view>
          </view>
          <view class="info-cell">
            <text class="info-label">姓名</text>
            <u-input
              v-model="infoForm.emergencyName2"
              placeholder="请输入真实姓名"
              :border="false"
              class="info-value"
              input-align="right"
              :placeholder-style="placeholderStyle"
              @blur="validateEmergencyName(2)"
            />
          </view>
          <view class="info-cell">
            <text class="info-label" style="width: 120rpx; flex-shrink: 0"
              >手机号</text
            >
            <view class="info-cell-flex" style="flex: 1; align-items: center">
              <u-input
                v-model="infoForm.emergencyPhone2"
                placeholder="请输入联系人手机号"
                :border="false"
                class="info-value"
                input-align="right"
                :placeholder-style="placeholderStyle"
                style="width: 340rpx"
                type="number"
                maxlength="11"
              />
              <!-- <image
                src="/static/financial/contact.png"
                style="width: 44rpx; height: 44rpx; margin-left: 8rpx"
                @click="chooseContact(2)"
              /> -->
            </view>
          </view>
        </view>
        <view style="height: 300rpx"></view>
        <view class="info-bottom">
          <u-button :custom-style="btnStyle" @click="nextStep" class="submit-btn"
            >下一步</u-button
          >
          <view class="info-safe-tip">
            <image src="/static/financial/desc.png" mode="scaleToFill" />
            平台承诺保护您的信息安全
          </view>
        </view>

        <!-- 自定义选择器弹窗 -->
        <view v-if="pickerShow" class="reason-modal-mask">
          <view class="reason-modal">
            <view class="reason-modal-header">
              <view class="reason-modal-title">{{ getPickerTitle() }}</view>
              <u-icon
                class="close_btn"
                name="close"
                size="36"
                color="#bbb"
                @click="pickerShow = false"
              ></u-icon>
            </view>
            <view class="reason-modal-list">
              <view
                v-for="(item, idx) in pickerColumns[0]"
                :key="idx"
                class="reason-modal-item"
                @click="selectPickerItem(idx)"
              >
                <view class="reason-modal-label">{{ item }}</view>
                <view class="reason-modal-radio">
                  <image
                    v-if="selectedPickerIndex === idx"
                    src="/static/gou.png"
                    mode="scaleToFill"
                  />
                  <view v-else></view>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <view v-if="step === 2">
        <!-- 标题和额度提示 -->
        <view class="title-bar_box">
          <view class="title-bar">
            <text class="main-title">上传本人身份证</text>
            <text class="quota"
              >预计可借款<text class="quota-num">20万</text>额度</text
            >
          </view>
          <view class="desc">
            <image src="/static/financial/desc.png" mode="scaleToFill" />
            用于身份安全核验、反洗钱链路评估，资料将被加密保护
          </view>
          <!-- 步骤条 -->
          <view class="custom-steps-bar">
            <!-- 标题 -->
            <view class="steps-labels">
              <view
                class="step-label"
                v-for="(item, idx) in steps"
                :key="item.name"
                :class="{
                  left: idx === 0,
                  right: idx === steps.length - 1,
                }"
              >
                {{ item.name }}
              </view>
            </view>
            <!-- 步进器 -->
            <view class="steps-track">
              <template v-for="(item, idx) in steps">
                <view class="step-icon" :key="'icon' + idx">
                  <!-- 已完成 -->
                  <view class="step-done" v-if="idx < step">
                    <image
                      src="/static/financial/step_Act.png"
                      mode="scaleToFill"
                      class="step-icon-img"
                    />
                  </view>
                  <!-- 当前步骤（等待/激活） -->
                  <view class="step-active" v-else-if="idx === step">
                    <view class="step-yuan"></view>
                  </view>
                  <!-- 未激活 -->
                  <view v-else class="step-noActive"></view>
                </view>
                <view
                  v-if="idx < steps.length - 1"
                  class="step-line"
                  :class="{ active: idx < step }"
                  :key="'line' + idx"
                ></view>
              </template>
            </view>
          </view>
        </view>

        <view class="bank-card-section">
          <view class="bank-card-row">
            <text class="bank-card-label">持卡人</text>
            <u-input
              v-model="bankForm.name"
              placeholder="请输入持卡人姓名"
              :placeholder-style="placeholderStyle"
              :border="false"
              class="info-value"
              input-align="right"
              :disabled="true"
            />
          </view>
          <view class="bank-card-row">
            <text class="bank-card-label">银行卡号</text>
            <view class="bank-card-flex">
              <u-input
                v-model="bankForm.cardNo"
                placeholder="请输入银行卡号"
                :border="false"
                class="info-value"
                input-align="right"
                :placeholder-style="placeholderStyle"
                type="number"
              />
              <image
                src="/static/financial/bank-card.png"
                style="width: 36rpx; height: 36rpx; margin-left: 8rpx"
                mode="aspectFit"
                @click="scanBankCard"
              />
            </view>
          </view>
          <view class="bank-card-row">
            <text class="bank-card-label">手机号</text>
            <u-input
              v-model="bankForm.phone"
              placeholder="请输入银行预留手机号"
              :border="false"
              class="info-value"
              input-align="right"
              :placeholder-style="placeholderStyle"
              type="number"
              maxlength="11"
            />
          </view>
          <view class="bank-card-row">
            <text class="bank-card-label">验证码</text>
            <view class="bank-card-flex">
              <u-input
                v-model="bankForm.code"
                placeholder="请输入验证码"
                :border="false"
                class="info-value"
                input-align="right"
                :placeholder-style="placeholderStyle"
                type="number"
                maxlength="6"
              />
              <text
                class="get-code-btn"
                @click="getBankCode"
                :style="{
                  color: canGetCode ? '#ff6b35' : '#ccc',
                  marginLeft: '8rpx',
                  cursor: canGetCode ? 'pointer' : 'not-allowed',
                }"
                >{{ codeText }}</text
              >
            </view>
          </view>
        </view>
        <view class="bank-card-support" >
          <text style="color: #ff6b35; font-size: 24rpx" @click="showSupportBank">查看支持银行</text>
        </view>
        <view style="height: 300rpx"></view>
        <view class="info-bottom">
          <u-button
            :custom-style="btnStyle"
            @click="nextBank"
            class="submit-btn"
            >确认绑定</u-button
          >
          <view class="info-safe-tip">
            <image src="/static/financial/desc.png" mode="scaleToFill" />
            平台承诺保护您的信息安全
          </view>
        </view>
        <!-- 银行列表弹窗 -->
        <u-popup v-model="showBankList" mode="bottom" border-radius="24" :closeable="true">
          <view class="bank-list-content">
            <view class="bank-list-header">
              <text class="bank-list-title">支持银行</text>
              <image
                src="/static/close.png"
                class="bank-list-close"
                @click="closeBankList"
              />
            </view>
            <scroll-view scroll-y="true" class="bank-list-scroll">
              <view
                class="bank-list-item"
                v-for="(bank, idx) in bankList"
                :key="bank.name"
              >
                <image :src="bank.icon" class="bank-list-icon" />
                <text class="bank-list-name">{{ bank.name }}</text>
              </view>
            </scroll-view>
          </view>
        </u-popup>
      </view>
      <!-- <button @click="goToBill">goToBill</button> -->
      <!-- <button @click="cs">身份证</button> -->
      <m-city
        :provinceData="cityList"
        headTitle="区域选择"
        ref="cityPicker"
        pickerSize="4"
        @funcValue="onCitySelected"
        v-if="cityPickerShow"
      />
      <u-popup v-model="showAuthModal" mode="center" :mask-close-able="false" border-radius="16">
        <view class="auth-modal">
          <view class="auth-modal-logo">
            <image src="/static/logo.png" mode="aspectFit" style="width: 100rpx; height: 100rpx;border-radius: 12rpx;" />
          </view>
          <!-- <view class="auth-modal-title">服务授权</view> -->
          <view class="auth-modal-desc">您即将使用<text>臻享卡</text>服务</view>
          <!-- <view class="auth-modal-protocol">
            <u-checkbox v-model="authChecked" shape="circle" active-color="#ff6b35" />
            <text style="margin-left: -18rpx;">如您决定使用卡包服务的，请勾选同意</text>
            <text class="auth-link auth-linkLast" @click="openRegisterProtocol">《注册协议》</text>
            <text class="auth-link" @click="openPrivacyProtocol">《隐私协议》</text>
          </view> -->
          <button class="auth-modal-btn" @click="handleAuthLogin">确定</button>
        </view>
      </u-popup>
      <u-popup v-model="showCreditModal" mode="bottom" border-radius="16" :closeable="true">
        <view style="padding: 60rpx 40rpx; 0">
          <view class="credit-modal-header">
                <text class="credit-modal-title">{{ creditModalTitle }}</text>
                <!-- <u-icon name="close" size="36" color="#bbb" class="credit-modal-close" @click="closeCreditModal" /> -->
          </view>
          <u-tabs :list="tbaList"  :current="currentTitle" @change="tabChange"
             active-color="#333" inactive-color="#666" :bar-style="barStyle"
            ></u-tabs>
            <view class="credit-modal">
             
              <scroll-view scroll-y class="credit-modal-content">
                <view class="credit-modal-text">
                  {{ creditModalContent }}
                </view>
              </scroll-view>
              <button class="credit-modal-btn" @click="closeCreditModal">同意协议并申请</button>
            </view>
        </view>
        
      </u-popup>
    </view>
  </view>
</template>

<script>
import storage from "@/utils/storage.js";
import { upload } from "@/api/common.js";
import mCity from "@/components/m-city/m-city.vue";
import { mapState, mapMutations } from 'vuex';
import { checkBankno } from "@/utils/Foundation.js";
import { mobile } from "@/utils/RegExp.js";
import { getIdAuthInfo, threeElementVerify,getIdAuthParam} from '@/api/safe.js';
import { getDictManyList, getBankCardAuthCode,addBankUser } from '@/api/common.js';
import { perfectUserInfo, ocrBankCard, getUserInfo, getUserAuthStepInfo } from '@/api/members.js';
// #ifdef APP-PLUS
const ocr  = uni.requireNativePlugin('DC-WBOCRService');
// #endif

export default {
  components: {
    mCity,
  },
  data() {
    return {
      storage,
      feedBack: {
        type: "FUNCTION", //默认反馈问题为 '功能相关'
      },
      placeholderStyle: "font-size: 32rpx;color: #999;",
      action: upload, //图片上传地址
      step: 0,
      loading: true, // 新增 loading 状态
      steps: [{ name: "身份认证" }, { name: "完善信息" }, { name: "绑卡激活" }],
      frontList: [],
      backList: [],
      frontList1: [],
      backList1: [],
      checked: false,
      frontShow: true,
      backShow: true,
      idInfoShow: false,
      idInfo: {
        name: "张三",
        id: "******************",
        org: "浙江省公安局",
        date: "2024.02.02~2035.02.02",
      },
      idInfoEdit: {
        name: false,
        id: false,
        org: false,
        date: false,
      },
      infoForm: {
        education: "",
        marriage: "",
        city: "",
        address: "",
        income: "",
        industry: "",
        job: "",
        emergencyRelation: "",
        emergencyName: "",
        emergencyPhone: "",
        emergencyRelation2: "",
        emergencyName2: "",
        emergencyPhone2: "",
      },
      pickerShow: false,
      pickerColumns: [],
      pickerField: "",
      inputValue: "",
      selectedPickerIndex: null,
      cityPickerShow: false,
      cityList: [
        {
          id: "",
          localName: "请选择",
          children: [],
        },
      ],
      cityPath: "",
      cityIds: [],
      bankForm: {
        name: "",
        cardNo: "",
        phone: "",
        code: "",
      },  
      codeText: "获取验证码",
      canGetCode: true,
      showBankList: false,
      bankList: [
        { name: "中国银行", icon: "/static/financial/bank-icon.png" },
        { name: "中国工商银行", icon: "/static/financial/bank-icon.png" },
        { name: "中国建设银行", icon: "/static/financial/bank-icon.png" },
        { name: "中国农业银行", icon: "/static/financial/bank-icon.png" },
        { name: "中国邮政储蓄银行", icon: "/static/financial/bank-icon.png" },
        { name: "中国民生银行", icon: "/static/financial/bank-icon.png" },
        { name: "中国兴业银行", icon: "/static/financial/bank-icon.png" },
        { name: "中国平安银行", icon: "/static/financial/bank-icon.png" },
        { name: "中国中信银行", icon: "/static/financial/bank-icon.png" },
        { name: "中国光大银行", icon: "/static/financial/bank-icon.png" },
        { name: "中国华夏银行", icon: "/static/financial/bank-icon.png" },
        { name: "中国浦发银行", icon: "/static/financial/bank-icon.png" },
        { name: "中国广发银行", icon: "/static/financial/bank-icon.png" },
        { name: "中国浙商银行", icon: "/static/financial/bank-icon.png" },
        { name: "中国恒丰银行", icon: "/static/financial/bank-icon.png" },
        { name: "中国渤海银行", icon: "/static/financial/bank-icon.png" },
        { name: "中国农村商业银行",icon: "/static/financial/bank-icon.png" },
        { name: "中国城市商业银行", icon: "/static/financial/bank-icon.png" },
      ],
      showAuthModal: true,
      authChecked: false,
      showCreditModal: false,
      creditModalTitle: '',
      creditModalContent: '',
      dictOptions: {
        education: [],
        marriage_status: [],
        monthPrice: [],
        industry: [],
        jobType: [],
        contact_relationship: [],
      },
      tbaList:[
        {
					name: '用户授权协议'
				}, 
        {
					name: '个人委托担保意向及信息授权书'
				}, 
        {
					name: '综合授权书',
				},
        {
					name: '个人电子签名签署授权书'
				}, 
        {
					name: '非在校大学生承诺函'
				}, 
        {
					name: '数字证书使用协议',
				}
      ],
      barStyle: {
        backgroundColor: '#FF5134'
      },
      currentTitle: 0
    };
  },
  computed: {
    ...mapState(['hasAgreedAuth']),
    canSubmit() {
      return (
        this.frontList1.length > 0 && this.backList1.length > 0 && this.checked
      );
    },
    btnText() {
      return this.step === this.steps.length - 1 ? "提交" : "下一步";
    },
    btnStyle() {
      return {
        width: "100%",
        height: "100rpx",
        "border-radius": "86rpx",
        "font-size": "32rpx",
      };
    },
    canSubmitInfo() {
      // 所有字段必填
      const f = this.infoForm;
      return (
        f.education &&
        f.marriage &&
        f.city &&
        f.address &&
        f.address.length > 7 &&
        f.income &&
        f.industry &&
        f.job &&
        // 紧急联系人1验证
        f.emergencyRelation &&
        f.emergencyName &&
        /^[\u4e00-\u9fa5]{2,10}$/.test(f.emergencyName) &&
        f.emergencyPhone &&
        f.emergencyPhone.length === 11 &&
        // 紧急联系人2验证
        f.emergencyRelation2 &&
        f.emergencyName2 &&
        /^[\u4e00-\u9fa5]{2,10}$/.test(f.emergencyName2) &&
        f.emergencyPhone2 &&
        f.emergencyPhone2.length === 11 &&
        // 两个手机号不能相同
        f.emergencyPhone !== f.emergencyPhone2
      );
    },
  },
  methods: {
    ...mapMutations(['setHasAgreedAuth']),
    tabChange(index){
      this.currentTitle = index;
    },
    // 验证真实姓名
    validateRealName() {
      const name = this.idInfo.name;

      if (!name) {
        return; // 空值不提示，等提交时统一提示
      }

      if (!/^[\u4e00-\u9fa5]{2,10}$/.test(name)) {
        uni.showToast({
          title: '真实姓名必须是2-10个中文字符',
          icon: 'none'
        });
        // 清空不符合要求的输入
        this.idInfo.name = '';
      }
    },
    // 验证紧急联系人姓名
    validateEmergencyName(contactIndex) {
      const nameField = contactIndex === 1 ? 'emergencyName' : 'emergencyName2';
      const name = this.infoForm[nameField];

      if (!name) {
        return; // 空值不提示，等提交时统一提示
      }

      if (!/^[\u4e00-\u9fa5]{2,10}$/.test(name)) {
        uni.showToast({
          title: `紧急联系人${contactIndex}姓名必须是2-10个中文字符`,
          icon: 'none'
        });
        // 清空不符合要求的输入
        this.infoForm[nameField] = '';
      }
    },
    onFrontRead(e) {
      this.frontList = [e.file];
      this.frontList1 = []; // 新增：清空已识别图片
      this.idInfoShow = false;
    },
    onFrontRemove(index) {
      this.frontList.splice(index, 1);
      this.frontList1 = [];
      this.idInfoShow = false;
    },
    onFrontUploaded(lists) {
      let images = [];
      console.log('@@@@@@@@@@@@@@@@@@@@@@@@@',lists);
      lists.forEach((item) => {
        if (item.response && item.response.result) {
          images.push(item.response.result);
        }
      });
      this.frontList1 = images;
      // this.frontShow = false;
      this.tryFetchIdInfo();
    },
    onBackRead(e) {
      this.backList = [e.file];
      this.backList1 = []; // 新增：清空已识别图片
      this.idInfoShow = false;
    },
    onBackRemove(index) {
      this.backList.splice(index, 1);
      this.backList1 = [];
      this.idInfoShow = false;
    },
    onBackUploaded(lists) {
      let images = [];
      lists.forEach((item) => {
        if (item.response && item.response.result) {
          images.push(item.response.result);
        }
      });
      this.backList1 = images;
      this.backShow = false;
      this.tryFetchIdInfo();
    },
    async tryFetchIdInfo() {
      // 兜底，先关闭loading
      uni.hideLoading();
      // 正反面都上传后才请求
      if (this.frontList1.length > 0 && this.backList1.length > 0) {
        uni.showLoading({ title: '识别中...' });
        try {
          const res = await getIdAuthInfo({
            idCardFront: this.frontList1[0],
            idCardBack: this.backList1[0]
          });
          uni.hideLoading();
          if (res && res.data && res.data.result) {
            const { realName, idCard } = res.data.result;
            this.idInfo = { ...this.idInfo, name: realName || '', id: idCard || '' };
            this.idInfoShow = true;
          } else {
            uni.showToast({ title: '未识别到身份证信息', icon: 'none' });
          }
        } catch (e) {
          uni.hideLoading();
          uni.showToast({ title: '识别失败', icon: 'none' });
        }
      }
    },
    openAgreement() {
      this.creditModalTitle = '平台服务协议';
      this.creditModalContent = `【重要提示】
尊敬的客户，为保护您的合法权益，并获得您的同意授权使用您的个人信息，请您完整地阅读本授权书，特别是免除或减轻我们及我们的关联方、合作机构的条款，我们对重要条款进行了加粗以便您重点关注和阅读。当您勾选同意《个人信息授权书》时，即表示您已同意我们按照本授权书来合法使用和保护您的个人信息，本授权书即时生效。若您不同意本授权书的任意条款，请勿勾选同意，并立即停止授权。如有疑问请联系客服热线400 0790 0788。
定义用户承诺在仔细阅读和完全接受本协议项下全部条款的基础上使用本平台的各项服务。用户接受本协议点击"同意．同意并注册"按钮或其他类似意思表示完成注册即表示完全接受以上述及的所有条款。

。【重要提示】
尊敬的客户，为保护您的合法权益，并获得您的同意授权使用您的个人信息，请您完整地阅读本授权书，特别是免除或减轻我们及我们的关联方、合作机构的条款，我们对重要条款进行了加粗以便您重点关注和阅读。当您勾选同意《个人信息授权书》时，即表示您已同意我们按照本授权书来合法使用和保护您的个人信息，本授权书即时生效。若您不同意本授权书的任意条款，请勿勾选同意，并立即停止授权。如有疑问请联系客服热线400 0790 0788。
定义用户承诺在仔细阅读和完全接受本协议项下全部条款的基础上使用本平台的各项服务。用户接受本协议点击"同意．同意并注册"按钮或其他类似意思表示完成注册即表示完全接受以上述及的所有条款。

`;
      this.showCreditModal = true;
    },
    async submit() {
      if (this.frontList1.length === 0) {
        return uni.showToast({ title: "请上传身份证正面", icon: "none" });
      }
      if (this.backList1.length === 0) {
        return uni.showToast({ title: "请上传身份证反面", icon: "none" });
      }
      if (!this.checked) {
        return uni.showToast({ title: "请勾选同意协议", icon: "none" });
      }
      // step 0: 校验身份证三要素
      if (this.step === 0) {
        const name = this.idInfo.name;
        const id = this.idInfo.id;
        if (!name || !id) {
          return uni.showToast({ title: "请先识别身份证信息", icon: "none" });
        }
        // 验证真实姓名格式
        if (!/^[\u4e00-\u9fa5]{2,10}$/.test(name)) {
          return uni.showToast({ title: "真实姓名必须是2-10个中文字符", icon: "none" });
        }
        uni.showLoading({ title: '校验中...' });
        try {
          const res = await threeElementVerify({ realName: name, idCard: id });
          uni.hideLoading();
          if (res && res.data && res.data.success && res.data.code === 200) {
            const code = res.data.result;
            this.step++;
          }
        } catch (e) {
          uni.hideLoading();
          uni.showToast({ title: '身份证信息校验失败', icon: 'none' });
        }
        return;
      }
      // 其他步骤
      this.step++;
    },
    openPicker(field) {
      this.pickerField = field;
      // 动态数据映射
      const dictMap = {
        education: 'education',
        marriage: 'marriage_status',
        income: 'monthPrice',
        industry: 'industry',
        job: 'jobType',
        emergencyRelation: 'contact_relationship',
        emergencyRelation2: 'contact_relationship',
      };
      if (dictMap[field] && this.dictOptions[dictMap[field]]) {
        this.pickerColumns = [this.dictOptions[dictMap[field]].map(item => item.label)];
      } else {
        this.pickerColumns = [[]];
      }
      // 设置当前选中项的索引
      this.selectedPickerIndex = this.pickerColumns[0].findIndex(
        (item) => item === this.infoForm[field]
      );
      this.pickerShow = true;
    },
    selectPickerItem(index) {
      this.selectedPickerIndex = index;
      this.infoForm[this.pickerField] = this.pickerColumns[0][index];
      setTimeout(() => {
        this.pickerShow = false;
      }, 200);
    },
    getPickerTitle() {
      const titles = {
        education: "请选择您的学历",
        marriage: "请选择婚姻状况",
        city: "请选择居住城市",
        income: "请选择月收入",
        industry: "请选择所属行业",
        job: "请选择您的职业",
        emergencyRelation: "请选择紧急联系人关系",
        emergencyRelation2: "请选择紧急联系人关系",
      };
      return titles[this.pickerField] || "请选择";
    },
    async nextStep() {
      // 校验基本信息
      const f = this.infoForm;
      if (!f.education)
        return uni.showToast({ title: "请选择学历", icon: "none" });
      if (!f.marriage)
        return uni.showToast({ title: "请选择婚姻状况", icon: "none" });
      if (!f.city)
        return uni.showToast({ title: "请选择居住城市", icon: "none" });
      if (!f.address)
        return uni.showToast({ title: "请输入详细地址", icon: "none" });
      if (f.address.length <= 7)
        return uni.showToast({ title: "详细地址必须大于7个字符", icon: "none" });
      if (!f.income)
        return uni.showToast({ title: "请选择月收入", icon: "none" });
      if (!f.industry)
        return uni.showToast({ title: "请选择所属行业", icon: "none" });
      if (!f.job) return uni.showToast({ title: "请选择职业", icon: "none" });

      // 校验紧急联系人1
      if (!f.emergencyRelation)
        return uni.showToast({ title: "请选择紧急联系人1关系", icon: "none" });
      if (!f.emergencyName)
        return uni.showToast({ title: "请输入紧急联系人1姓名", icon: "none" });
      if (!/^[\u4e00-\u9fa5]{2,10}$/.test(f.emergencyName))
        return uni.showToast({ title: "紧急联系人1姓名必须是2-10个中文字符", icon: "none" });
      if (!f.emergencyPhone || f.emergencyPhone.length !== 11)
        return uni.showToast({
          title: "请输入11位紧急联系人1手机号",
          icon: "none",
        });

      // 校验紧急联系人2
      if (!f.emergencyRelation2)
        return uni.showToast({ title: "请选择紧急联系人2关系", icon: "none" });
      if (!f.emergencyName2)
        return uni.showToast({ title: "请输入紧急联系人2姓名", icon: "none" });
      if (!/^[\u4e00-\u9fa5]{2,10}$/.test(f.emergencyName2))
        return uni.showToast({ title: "紧急联系人2姓名必须是2-10个中文字符", icon: "none" });
      if (!f.emergencyPhone2 || f.emergencyPhone2.length !== 11)
        return uni.showToast({
          title: "请输入11位紧急联系人2手机号",
          icon: "none",
        });

      // 两个手机号不能相同
      if (f.emergencyPhone === f.emergencyPhone2) {
        return uni.showToast({
          title: "两个紧急联系人手机号不能相同",
          icon: "none",
        });
      }

      // 组装API参数
      const params = {
        education: this.getDictValue('education', f.education),
        marriageStatus: this.getDictValue('marriage_status', f.marriage),
        liveCity: this.cityPath || '',
        liveCityPath: this.cityIds.join(',') || '',
        liveDetailAddress: f.address,
        monthPrice: this.getDictValue('monthPrice', f.income),
        industry: this.getDictValue('industry', f.industry),
        jobType: this.getDictValue('jobType', f.job),
        contactList: [
          {
            contactRelationship: this.getDictValue('contact_relationship', f.emergencyRelation),
            contactName: f.emergencyName,
            contactPhone: f.emergencyPhone
          },
          {
            contactRelationship: this.getDictValue('contact_relationship', f.emergencyRelation2),
            contactName: f.emergencyName2,
            contactPhone: f.emergencyPhone2
          }
        ]
      };
      uni.showLoading({ title: '提交中...' });
      try {
        const res = await perfectUserInfo(params);
        uni.hideLoading();
        if (res && res.data && res.data.success) {
          this.step++;
        } else {
          uni.showToast({ title: res.data && res.data.message ? res.data.message : '提交失败', icon: 'none' });
        }
      } catch (e) {
        uni.hideLoading();
        uni.showToast({ title: '提交失败', icon: 'none' });
      }
    },
    // 辅助方法：根据label查找字典值
    getDictValue(dictKey, label) {
      var arr = this.dictOptions[dictKey] || [];
      var found = arr.find(function(item) { return item.label === label });
      return found ? found.value : '';
    },
    showCityPicker() {
      this.cityPickerShow = true;
      this.$nextTick(() => {
        this.$refs.cityPicker.show();
      });
    },
    onCitySelected(e) {
      this.cityIds = e.map((item) => item.id);
      this.cityPath = e.map((item) => item.localName).join(",");
      this.infoForm.city = this.cityPath;
      this.cityPickerShow = false;
    },
    chooseContact(idx = 1) {
      // #ifdef APP-PLUS
      if (plus.contacts.choose) {
        plus.contacts.choose(
          (contact) => {
            if (
              contact &&
              contact.phoneNumbers &&
              contact.phoneNumbers.length > 0
            ) {
              if (idx === 1) {
                this.infoForm.emergencyPhone = contact.phoneNumbers[0].value;
              } else {
                this.infoForm.emergencyPhone2 = contact.phoneNumbers[0].value;
              }
            } else {
              uni.showToast({ title: "未获取到手机号", icon: "none" });
            }
          },
          (e) => {
            if (e.code !== 12) {
              uni.showToast({ title: "获取联系人失败", icon: "none" });
            }
          }
        );
      } else {
        plus.contacts.getAddressBook(
          plus.contacts.ADDRESSBOOK_PHONE,
          (addressbook) => {
            addressbook.find(
              ["displayName", "phoneNumbers"],
              (contacts) => {
                let newArrary = contacts
                  .filter(
                    (item) => item.phoneNumbers && item.phoneNumbers.length > 0
                  )
                  .map((item) => ({
                    name: item.displayName || "未知名称",
                    mobile: item.phoneNumbers[0].value,
                  }));
                if (newArrary.length === 0) {
                  uni.showToast({ title: "未获取到联系人", icon: "none" });
                  return;
                }
                uni.showActionSheet({
                  itemList: newArrary.map((c) => `${c.name} ${c.mobile}`),
                  success: (res) => {
                    if (idx === 1) {
                      this.infoForm.emergencyPhone =
                        newArrary[res.tapIndex].mobile;
                    } else {
                      this.infoForm.emergencyPhone2 =
                        newArrary[res.tapIndex].mobile;
                    }
                  },
                });
              },
              (e) => {
                uni.showToast({ title: "查找联系人失败", icon: "none" });
              }
            );
          },
          (e) => {
            uni.showToast({ title: "获取通讯录失败", icon: "none" });
          }
        );
      }
      // #endif

      // #ifdef MP-WEIXIN
      uni.chooseContact({
        success: (res) => {
          if (res.phoneNumber) {
            if (idx === 1) {
              this.infoForm.emergencyPhone = res.phoneNumber;
            } else {
              this.infoForm.emergencyPhone2 = res.phoneNumber;
            }
          }
        },
      });
      // #endif
    },
    getBankCode() {
      if (!this.bankForm.name) {
        return uni.showToast({ title: '请输入持卡人姓名', icon: 'none' });
      }
      if (!this.bankForm.phone || this.bankForm.phone.length !== 11) {
        return uni.showToast({ title: "请输入正确手机号", icon: "none" });
      }
      if (!this.bankForm.cardNo) {
        return uni.showToast({ title: "请输入银行卡号", icon: "none" });
      }
      if (!this.canGetCode) return;
      this.canGetCode = false;
      this.codeText = "60s";
      let sec = 60;
      let timer = setInterval(() => {
        sec--;
        this.codeText = sec + "s";
        if (sec <= 0) {
          clearInterval(timer);
          this.canGetCode = true;
          this.codeText = "获取验证码";
        }
      }, 1000);
      // 判断平台
      let clientType = 'H5';
      // #ifdef APP-PLUS
      clientType = 'APP';
      // #endif
      // #ifdef H5
      clientType = 'H5';
      // #endif
      getBankCardAuthCode({
        mobile: this.bankForm.phone,
        cardNumber: this.bankForm.cardNo,
        clientType: clientType,
        realName:this.bankForm.name,
      }).then(res => {
        if(res && res.data && res.data.success) {
          uni.showToast({ title: '验证码已发送', icon: 'success' });
        } else {
          uni.showToast({ title: res.data && res.data.message ? res.data.message : '验证码发送失败', icon: 'none' });
        }
      }).catch(() => {
        uni.showToast({ title: '验证码发送失败', icon: 'none' });
      });
    },
    showSupportBank() {
      this.showBankList = true;
    },
    closeBankList() {
      this.showBankList = false;
    },
    nextBank() {
      // 校验持卡人
      if (!this.bankForm.name) {
        return uni.showToast({ title: '请输入持卡人姓名', icon: 'none' });
      }

      if (!this.bankForm.cardNo) {
        return uni.showToast({ title: '请输入银行卡号', icon: 'none' });
      }
      // 校验银行卡号
      console.log('校验银行卡号',this.bankForm.cardNo);
      
      if (!checkBankno(this.bankForm.cardNo)) {
        return uni.showToast({ title: '请输入正确的银行卡号', icon: 'none' });
      }
      // 校验手机号
      if (!this.bankForm.phone || !mobile.test(this.bankForm.phone)) {
        return uni.showToast({ title: '请输入正确的手机号', icon: 'none' });
      }
      // 校验验证码
      if (!this.bankForm.code || this.bankForm.code.length < 4) {
        return uni.showToast({ title: '请输入验证码', icon: 'none' });
      }
      // 判断平台
      let clientType = 'H5';
      // #ifdef APP-PLUS
      clientType = 'APP';
      // #endif
      // #ifdef H5
      clientType = 'H5';
      // #endif
      // 调用接口
      const params = {
        cardNumber: this.bankForm.cardNo,
        verifyCode: this.bankForm.code,
        clientType: clientType,
      };
      addBankUser(params).then(res => {
        if (res && res.data && res.data.success) {
          //关闭当前页跳转
          uni.showToast({ title: '绑定成功', icon: 'success' });
          setTimeout(() => {
            uni.redirectTo({ url: '/pages/financial/faceAuth' });
          }, 300);
          
        } else {
          uni.showToast({ title: res.data && res.data.message ? res.data.message : '绑卡失败', icon: 'none' });
        }
      }).catch(() => {
          this.$u.toast.clear();
          uni.showToast({ title: '网络错误', icon: 'none' });
      });
    },
    handleAuthLogin() {
      // if (!this.authChecked) {
      //   return uni.showToast({ title: '请先同意相关协议', icon: 'none' });
      // }
      this.setHasAgreedAuth(true);
      uni.setStorageSync('hasAgreedAuth', true);
      this.showAuthModal = false;
      // 这里写登录/授权逻辑checked
    },
    openRegisterProtocol() {
      this.creditModalTitle = '个人征信授权书';
      this.creditModalContent = `重要提示：
为维护授权人(以下简称"本人"或"您")的合法权益,请您签署《个人信息共享授权协议》(以下简称"本授权书")。仔细阅读、充分理解本授权书各条款内容,其中重要条款和加粗部分请您重点阅读。您授权后所征信授权相关个人信息或征信授权相关授权将用于如下事项：
一、当您通过本平台使用我们提供的【第三方产品推荐】服务时,为切实保障您的合法权益,您的个人信息会被用于相关审核。为了解您的信用状况,请您使用本协议授权本平台,并同意我们为您向相关征信机构申请、获取、保存、整理、使用您的相关信息。如果您拒绝授权或终止授权,将无法正常使用相关服务。
二、如需详细内容,请以协议原文及实际页面为准。
三、如您点击下方"同意协议并申请"按钮,即视为您已阅读并同意本授权协议("知悉并同意"按钮即为您同意本授权书的意思表示)。`;
      this.showCreditModal = true;
    },
    openPrivacyProtocol() {
      this.creditModalTitle = '隐私协议';
      this.creditModalContent = `这里是隐私协议的内容，请根据实际内容填写。`;
      this.showCreditModal = true;
    },
    closeCreditModal() {
      this.showCreditModal = false;
      // 自动勾选服务授权并登录
      if (!this.checked) {
        this.checked = true;
      }
      // if (this.showAuthModal) {
      //   this.handleAuthLogin();
      // }
    },
    scanBankCard() {
      uni.chooseImage({
        count: 1,
        sourceType: ['camera', 'album'],
        success: (res) => {
          const filePath = res.tempFilePaths[0];
          uni.showLoading({ title: '上传中...' });
          uni.uploadFile({
            url: this.action, // 这里用身份证上传用的 action 地址
            filePath,
            name: 'file',
            formData: { directoryPath: 'bankCard' },
            header: { accessToken: storage.getAccessToken() },
            success: async (uploadRes) => {
              uni.hideLoading();
              let result = JSON.parse(uploadRes.data);
              if (result && result.result) {
                // result.result 就是服务器图片地址
                // 自动识别银行卡号
                uni.showLoading({ title: '识别中...' });
                try {
                  const ocrRes = await ocrBankCard({ idCardFront: result.result });
                  uni.hideLoading();
                  if (ocrRes && ocrRes.data && ocrRes.data.code === 200) {
                    this.bankForm.cardNo = ocrRes.data.result;
                    uni.showToast({ title: '识别成功', icon: 'success' });
                  } else {
                    uni.showToast({ title: '未识别到银行卡号', icon: 'none' });
                  }
                } catch (e) {
                  uni.hideLoading();
                  uni.showToast({ title: '识别失败', icon: 'none' });
                }
              } else {
                uni.showToast({ title: '上传失败', icon: 'none' });
              }
            },
            fail: () => {
              uni.hideLoading();
              uni.showToast({ title: '上传失败', icon: 'none' });
            }
          });
        }
      });
    },
    goToBill(){
      uni.navigateTo({
        url: '/pages/financial/reviewing'
      });
    },
    cs(){
      getIdAuthParam().then(res => {
        const data = res.data.result;
        console.log('组装参数并调用SDK',res);
        // 组装参数并调用SDK
        ocr.startOCR({
          version : "1.0.0",
          appId :  data.appId,
          nonce :  data.nonce,
          userId :  data.userId,
          sign :  data.sign,
          orderNo : data.orderNo,
          config: {
            "SDKType": 1,
            "needBothSidesRecognized":true
          }
        }, result => {
          console.log("【uni log】startOCR callback ================> result.");
          console.log(result);
          this.result = result.toString();
        });
      });
    },
    onFrontUploadError(err) {
      uni.hideLoading();
      uni.showToast({ title: '身份证正面上传失败，请重试', icon: 'none' });
      // 重置状态，显示上传按钮
      this.frontShow = true;
     
    },
    onFrontUploadSuccess(){
      uni.hideLoading();
      this.frontShow = false;
    },
    onBackUploadError(err) {
      uni.hideLoading();
      uni.showToast({ title: '身份证反面上传失败，请重试', icon: 'none' });
      // 重置状态，显示上传按钮
      this.backShow = true;
     
    },
    onBackUploadSuccess(){
      uni.hideLoading();
      this.backShow = false;
    },
  },
  onLoad() {
    // 优先本地持久化判断
    if (uni.getStorageSync('hasAgreedAuth')) {
      this.setHasAgreedAuth(true);
      this.showAuthModal = false;
    } else if (this.hasAgreedAuth) {
      this.showAuthModal = false;
    } else {
      this.showAuthModal = true;
    }
    // 获取用户授权到哪一步
    getUserAuthStepInfo().then(res => {
      console.log('用户授权到哪一步', res);
      if (res && res.data && res.data.success && res.data.result) {
        const r = res.data.result;
        this.bankForm.name = r.realName;
        if (r.riskStatus === 'P') {
          // 已完成所有步骤，跳转到审核页面
          uni.redirectTo({ url: '/pages/financial/reviewing' });
          // 跳转时不关闭loading，保持加载状态直到页面跳转
          return;
        } else if (r.bankBindStatus === 'Y') {
          // 已绑卡未做人脸识别，跳转到人脸识别页面
          uni.redirectTo({ url: '/pages/financial/faceAuth' });
          // 跳转时不关闭loading，保持加载状态直到页面跳转
          return;
        } else if (r.perfectInfoStatus === 'Y') {
          this.step = 2; // 绑卡激活
        } else if (r.realNameStatus === 'Y') {
          this.step = 1; // 完善信息
        } else {
          this.step = 0; // 身份认证
        }
      }
      // 只有在不需要跳转的情况下才关闭loading
      this.loading = false;
    }).catch(() => {
      this.loading = false;
    });
    // 获取字典项
    getDictManyList(["education","marriage_status","monthPrice","industry","jobType","contact_relationship"]).then(res => {
      if (res && res.data && res.data.result) {
        this.dictOptions = res.data.result;
      }
    });
    // 获取用户信息，自动填充手机号
    getUserInfo().then(res => {
      if (res && res.data.code == 200) {
        this.bankForm.phone = res.data.result.mobile;
      }
    });
  },
  watch: {
    'idInfo.name'(val) {
      if (val) {
        this.bankForm.name = val;
      }
    }
  },
};
</script>

<style lang="scss" scoped>
@import "@/uni.scss";
.id-auth-page {
  //   background: #fff;
}
.title-bar_box {
  width: 750rpx;
  //   height: 410rpx;
  background: linear-gradient(180deg, #fbeae1 0%, rgba(251, 234, 225, 0) 100%);
  border-radius: 0rpx 0rpx 0rpx 0rpx;
  padding: 34rpx 32rpx;
}
.title-bar {
  .main-title {
    font-size: 32rpx;
    font-weight: bold;
    font-family: "FZZhengHeiS-B-GB, FZZhengHeiS-B-GB";
    color: #444444;
  }
  .quota {
    color: #ff6b35;
    margin-left: 12rpx;
    font-size: 32rpx;
    font-weight: bold;
    font-family: "FZZhengHeiS-B-GB, FZZhengHeiS-B-GB";
  }
  .quota-num {
    font-family: "FZZhengHeiS-B-GB, FZZhengHeiS-B-GB";
    font-weight: bold;
    font-size: 36rpx;
    color: #ff5134;
    text-shadow: 2px -2px 0px #ffad94;
    text-stroke: 1px #ffffff;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }
}
.desc {
  margin: 6rpx 0 36rpx 0;
  display: flex;
  align-items: center;
  font-family: PingFang SC, PingFang SC;
  font-weight: 400;
  font-size: 24rpx;
  color: #666666;
  image {
    width: 32rpx;
    height: 32rpx;
  }
}

.custom-steps-bar,
.custom-steps-bar1 {
  width: 686rpx;
  background: #ffffff;
  border-radius: 20rpx;
  padding: 32rpx 0;
  .steps-labels {
    display: flex;
    justify-content: space-between;
    margin: 0 32rpx;
    font-weight: 400;
    font-size: 32rpx;
    color: #333333;
    .step-label {
      text-align: center;
      font-size: 26rpx;
      color: #222;
      &.left {
        text-align: left;
        margin-left: 10rpx;
      }
      &.right {
        text-align: right;
      }
    }
  }
  .steps-track {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 18rpx 80rpx 0;
    .step-icon {
      z-index: 1;
    }
    .step-done {
      width: 32rpx;
      height: 32rpx;
      border-radius: 50%;
      border: 3rpx solid #ff5134;
      background: #ff5134;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .step-active {
      width: 32rpx;
      height: 32rpx;
      border-radius: 50%;
      border: 3rpx solid #ff6b35;
      background: #fff;
      display: flex;
      align-items: center;
      justify-content: center;
      .step-yuan {
        width: 20rpx;
        height: 20rpx;
        background: #ff5134;
        border-radius: 50%;
      }
    }
    .step-icon-img {
      width: 32rpx;
      height: 32rpx;
    }
    .step-noActive {
      width: 20rpx;
      height: 20rpx;
      background: #dadada;
      border-radius: 50%;
    }
    .step-img {
      width: 32rpx;
      height: 32rpx;
      display: block;
      margin: 0 auto;
    }
    .step-line {
      flex: 1;
      height: 4rpx;
      background: #e5e5e5;
      margin: 0 -2rpx;
      z-index: 0;
    }
    .step-line.active {
      background: #ff6b35;
    }
  }
}
.custom-steps-bar1 {
  width: 100%;
}
.upload-section {
  display: flex;
  align-items: center;
  justify-content: space-evenly;
  margin-bottom: 32rpx;
  width: 686rpx;
  height: 256rpx;
  background: #ffffff;
  border-radius: 20rpx 20rpx 20rpx 20rpx;
  margin: 0 auto;
}

.upload-placeholder {
  position: relative;
  width: 100%;
  height: 180rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.idcard-bg {
  width: 304rpx;
  height: 196rpx;
  border-radius: 12rpx;
}
.upload-content {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.upload-content text {
  color: #ff6b35;
  font-size: 28rpx;
  margin-top: 8rpx;
}
.notice {
  margin: 20rpx auto 40rpx;
  .notice-card {
    display: block;
    image {
      width: 686rpx;
      height: 256rpx;
      margin: 0 auto;
      display: block;
    }
  }
}
.notice-title {
  color: #ff6b35;
  font-size: 24rpx;
  margin-bottom: 12rpx;
}
.notice-title .link {
  color: #ff6b35;
  text-decoration: underline;
}
.notice-list {
  display: flex;
  justify-content: space-between;
}
.notice-item {
  padding: 8rpx 16rpx;
  border-radius: 8rpx;
  background: #f5f5f5;
  color: #bbb;
  font-size: 24rpx;
}
.notice-item.standard {
  background: #e6fff2;
  color: #00c292;
}
.agreement {
  display: flex;
  align-items: center;
  margin-bottom: 32rpx;
  font-size: 24rpx;
  margin-left: 60rpx;
  text {
    margin-left: -20rpx;
  }
}
.agreement .link {
  color: #ff6b35;
  margin-left: 4rpx;
}
.info-bottom, .info-bottomIdAuth {
  width: 750rpx;
  height: 206rpx;
  background: #ffffff;
  box-shadow: 0rpx -4rpx 8rpx 0rpx rgba(0, 0, 0, 0.04);
  border-radius: 0rpx 0rpx 0rpx 0rpx;
  position: fixed;
  bottom: 0;
}
.info-bottomIdAuth {
  height: 250rpx;
  padding-top: 20rpx;
}
.submit-btn {
  width: 670rpx !important;
  height: 100rpx !important;
  margin-bottom: 16rpx;
  background: linear-gradient(90deg, #ff5235 0%, #ff9500 100%);
  margin: 32rpx auto 0;
  font-weight: 400;
  font-size: 32rpx;
  color: #ffffff !important;
  border: none !important;
}
.tip {
  font-weight: 400;
  font-size: 24rpx;
  color: #666666;
  margin: 20rpx 80rpx 0;
}
.upload-success-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.7);
  border-radius: 12rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 2;
}
.close-btn {
  position: absolute;
  top: 12rpx;
  right: 12rpx;
  font-size: 32rpx;
  color: #888;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 3;
}
.success-text {
  background: rgba(60, 60, 60, 0.8);
  color: #fff;
  font-size: 28rpx;
  border-radius: 32rpx;
  padding: 8rpx 24rpx;
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
}
.corner-tag {
  position: absolute;
  right: 0;
  bottom: 0;
  width: 120rpx;
  height: 60rpx;
}
/deep/ .u-list-item {
  height: 196rpx !important;
}
.upload-encrypt {
  width: 304rpx;
  height: 196rpx;
  position: relative;
  image {
    width: 100%;
    height: 100%;
  }
  .upload-encrypt-det {
    position: absolute;
    top: 0;
    right: 0;
    width: 32rpx;
    height: 32rpx;
  }
}
.id-info-card {
  //   background: #fff;
  border-radius: 12rpx;
  padding: 0 32rpx;
  .id-info-title {
    font-weight: 400;
    font-size: 24rpx;
    color: #666666;
    margin: 20rpx 22rpx;
  }
  .id-info-row-box {
    width: 686rpx;
    background: #ffffff;
    border-radius: 20rpx;
    padding: 32rpx 32rpx 20rpx 32rpx;
  }
  .id-info-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16rpx;
    padding-bottom: 20rpx;
    border-bottom: 1rpx solid rgba(0, 0, 0, 0.1);
    &:last-child {
      border-bottom: none;
      padding-bottom: 0;
    }
    .id-info-label {
      font-weight: 400;
      font-size: 28rpx;
      color: rgba(51, 51, 51, 0.55);
      // width: 140rpx;
      flex-shrink: 0;
      margin-right: 12rpx;
    }
    .id-info-input {
      flex: 1;
      font-size: 24rpx;
      color: #333;
      background: transparent;
      border: none;
      padding: 0 8rpx;
    }
    .id-info-edit {
      margin-left: 8rpx;
      cursor: pointer;
    }
  }
}
.info-title-box {
  padding: 32rpx 32rpx 0 32rpx;
  background: #fff;
}
.info-title {
  font-weight: 400;
  font-size: 40rpx;
  color: #333333;
}
.info-desc {
  font-weight: 400;
  font-size: 28rpx;
  color: #666666;
  margin-top: 12rpx;
}
.info-section {
  margin: 32rpx 32rpx 0 32rpx;
  background: #fff;
  border-radius: 20rpx;
  padding: 0 0 16rpx 0;
}
.info-group-title {
  font-weight: 500;
  font-size: 36rpx;
  color: #333333;
  padding: 24rpx 0 12rpx 0;
}
.info-cell {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 28rpx 0;
  border-bottom: 1rpx solid #f2f2f2;
  font-size: 28rpx;
  color: #333;
}
.info-label {
  color: #333;
  font-size: 28rpx;
  flex-shrink: 0;
}
.info-value {
  color: #999;
  font-size: 32rpx;
  margin-right: 16rpx;
  &.selected {
    color: #333;
  }
}
.info-cell-flex {
  display: flex;
}
.info-safe-tip {
  text-align: center;
  color: #ff6b35;
  font-size: 24rpx;
  margin: 16rpx auto 0;
  display: flex;
  align-items: center;
  justify-content: center;
  image {
    width: 32rpx;
    height: 32rpx;
    margin-right: 8rpx;
  }
}
.input-popup-box {
  padding: 32rpx;
  background: #fff;
  border-radius: 16rpx;
  min-width: 400rpx;
}
.reason-modal-mask {
  position: fixed;
  z-index: 9999;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  display: flex;
  align-items: flex-end;
  justify-content: center;
}
.reason-modal {
  width: 100vw;
  max-width: 100vw;
  background: #fff;
  border-radius: 24rpx 24rpx 0 0;
  padding-bottom: 32rpx;
  animation: modalIn 0.2s;
}
@keyframes modalIn {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}
.reason-modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 32rpx 0 32rpx;
  position: relative;
  text-align: center;
}
.close_btn {
  position: absolute;
  top: 40rpx;
  right: 40rpx;
}
.reason-modal-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  text-align: center;
  flex: 1;
}
.reason-modal-list {
  max-height: 600rpx;
  overflow-y: auto;
  margin: 24rpx 0 0 0;
}
.reason-modal-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 28rpx 32rpx;
  font-size: 30rpx;
  color: #333;
  cursor: pointer;
  background: #fff;
  &.selected {
    color: #ff5000;
    background: #fff7f0;
    font-weight: 500;
  }
}
.reason-modal-label {
  flex: 1;
}
.reason-modal-radio {
  margin-left: 24rpx;
  image {
    width: 32rpx;
    height: 32rpx;
  }
  view {
    width: 32rpx;
    height: 32rpx;
    border-radius: 50%;
    border: 1rpx solid #999999;
  }
}
/deep/ .u-input__input {
  min-height: 0 !important;
  font-size: 32rpx;
}
.bank-card-section {
  background: #fff;
  border-radius: 20rpx;
  padding: 10rpx 40rpx;
}
.bank-card-row {
  display: flex;
  align-items: center;
  padding: 28rpx 0;
  border-bottom: 1rpx solid #f2f2f2;
  &:last-child {
    border-bottom: none;
  }
}
.bank-card-label {
  color: #333;
  font-size: 28rpx;
  width: 160rpx;
  flex-shrink: 0;
}
.bank-card-flex {
  display: flex;
  align-items: center;
  flex: 1;
}
.bank-card-input {
  flex: 1;
  font-size: 32rpx;
  color: #333;
  background: transparent;
  border: none;
  padding: 0 8rpx;
}
.get-code-btn {
  color: #ff6b35;
  font-size: 28rpx;
  margin-left: 16rpx;
  user-select: none;
}
.bank-card-support {
  padding: 20rpx 40rpx;
  text-align: left;
}
.bank-list-content {
  padding: 32rpx;
}
.bank-list-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24rpx;
}
.bank-list-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}
.bank-list-close {
  width: 40rpx;
  height: 40rpx;
  cursor: pointer;
}
.bank-list-scroll {
  max-height: 600rpx;
  overflow-y: auto;
}
.bank-list-item {
  display: flex;
  align-items: center;
  padding: 28rpx 0;
  border-bottom: 1rpx solid #f2f2f2;
  &:last-child {
    border-bottom: none;
  }
}
.bank-list-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 24rpx;
}
.bank-list-name {
  font-size: 28rpx;
  color: #333;
}
.auth-modal {
  width: 560rpx;
  background: #fff;
  border-radius: 16rpx;
  padding: 60rpx 32rpx 32rpx 32rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.auth-modal-logo {
  width: 100rpx;
  height: 100rpx;
  background: #f5f5f5;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.auth-modal-title {
  font-weight: 400;
  font-size: 36rpx;
  color: #000000;
  margin-bottom: 12rpx;
}
.auth-modal-desc {
  font-weight: 400;
  font-size: 24rpx;
  color: #000000;
  margin-bottom: 32rpx;
  text {
    font-size: 28rpx;
    font-weight: 500;
  }
}
.auth-modal-protocol {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  font-size: 24rpx;
  color: #999;
  margin-bottom: 32rpx;
  margin-left: 30rpx;
}
.auth-link {
  color: #ff6b35;
  
}
.auth-linkLast {
  margin-left: 40rpx;
}
.auth-modal-btn {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(90deg, #ff5235 0%, #ff9500 100%);
  color: #fff;
  font-size: 32rpx;
  border: none;
  border-radius: 44rpx;
  margin-top: 8rpx;
  font-weight: 400;
}
.credit-modal {
  width: 100%;
  background: #fff;
  border-radius: 16rpx;
  padding: 0 0 32rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.credit-modal-header {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  margin-bottom: 40rpx;
  // padding: 32rpx 0 0 0;
}
.credit-modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #222;
}
.credit-modal-close {
  position: absolute;
  right: 32rpx;
  top: 32rpx;
}
.credit-modal-content {
  width: 100%;
  max-height: 716rpx;
  min-height: 400rpx;
  margin: 24rpx 0 0 0;
  background: #F7F7F7;
  border-radius: 12rpx;
  overflow-y: auto;
}
.credit-modal-text {
  font-size: 24rpx;
  color: #333;
  line-height: 1.7;
  padding: 0 32rpx;
}
.credit-modal-btn {
  width: 670rpx;
  height: 100rpx;
  background: linear-gradient(90deg, #ff5235 0%, #ff9500 100%);
  color: #fff;
  font-size: 30rpx;
  border: none;
  border-radius: 78rpx;
  margin-top: 32rpx;
  font-weight: bold;
  line-height: 100rpx;
}
.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100vh;
}
</style>
