<template>
  <view class="container">
    <u-navbar  :title="title" title-width="400" back-icon-color="#333333" title-color="#333333" ></u-navbar>
    <view class="content_box">
      <u-parse :html="content"  :tag-style="style"></u-parse>
    </view>
  </view>
</template>

<script>
import { getPrivacyDet } from "@/api/article";
export default {
  data() {
    return {
      title: '',
      content: '',
      protocolMap: {
        basic: {
          title: '臻小选基本功能隐私协议',
          content: `<b>重要提示：</b><br/>这是臻小选基本功能隐私协议内容示例。请根据实际情况补充。`
        },
        child: {
          title: '儿童个人信息保护规则',
          content: `<b>重要提示：</b><br/>这是儿童个人信息保护规则内容示例。请根据实际情况补充。`
        },
        permission: {
          title: '应用权限说明',
          content: `<b>重要提示：</b><br/>这是应用权限说明内容示例。请根据实际情况补充。`
        },
        thirdparty: {
          title: '个人信息共享授权协议',
          content: `<b>重要提示：</b><br/>为维护授权人(以下简称"本人"或"您")的合法权益，请在签署《个人信息共享授权协议》(以下简称"本授权书")前，仔细阅读、充分理解授权书各条款内容，其中重要条款以黑体加粗形式提示您。您点击本授权书所在页面按钮并进入下一页面或勾选本授权书即视为您已阅读授权书，并同意接受本授权书条款的约束，本授权书即成立或生效并生效。<br/><br/>您在本页面面使用我们推送提供的【第三方产品推荐】服务时，为了增加您的申请意愿通过机会，您的个人信息会进行共享。为了给您综合的建议，您需使用本服务部分或所有细则本协议约定的内容(特别是以粗体下划线标示的内容)。如您不同意本协议的全部内容或部分内容，或者无法准确理解本协议任何条款的含义，请不要进行授权及后续操作。<br/><br/>二、您通过本协议以明示电子形式立如勾通过网络页面点击"同意授权"即视为您同意"接受用户隐私协议"(见页面相关声明)，即视您已阅读并同意本协议的全部内容和我们出于相关页面所述目的，将您的个人信息通过适当途径的方式提供给【供应商、相关服务商、营销服务的其他个人或单位】(以下简称"第三方")，账号将继续有效，短信信息将准能否通过能否申请相关服务从您知悉和同意方的约定为准。<br/><br/>三、本协议中共享的个人信息含个人敏感信息及其他个人信息<br/>包括但不限于：<br/>1.您的身份信息，包括但不限于您的姓名、性别、出生年月、住址、电子邮箱、电话号码、身份证号、工作信息、学历、婚姻状况、紧急联系人、家庭信息、银行卡信息、及通讯录等信息。<br/>2.您的信用信息，包括但不限于您的征信记录和还款记录、信用评分、信用报告、逾期信息、法院判决、被执行信息、失信被执行人信息等。<br/>3.您的设备信息，包括但不限于设备型号、操作系统、唯一设备标识符、位置信息、IP地址、MAC地址等。<br/>4.您的网络信息，包括但不限于浏览器类型、浏览记录、点击记录、Cookie信息等。<br/>5.其他为实现本协议目的所需的个人信息。<br/><br/>四、我们承诺将严格按照相关法律法规及本协议约定，采取必要的安全措施保护您的个人信息安全，防止信息泄露、损毁、丢失、被盗用或被篡改。<br/><br/>五、如您对本协议有任何疑问，可通过页面提供的联系方式与我们联系。`
        },
        personalized: {
          title: '个性化内容推荐',
          content: `<b>重要提示：</b><br/>这是个性化内容推荐内容示例。请根据实际情况补充。`
        }
      }, 
      content:'',
      style: {
        img: "width: 710rpx;height:502rpx",
         
      },
    }
  },
  onLoad(options) {
    // options.type 由跳转时传递
    const type = options.type || 'basic';
    
    this.fetchProtocolContent(options.type);
  },
  methods: {
    async fetchProtocolContent(id) {
      const res = await getPrivacyDet(id);
      if (res.data.success) {
        this.title = res.data.result.title
        this.content = res.data.result.content
      }
     
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  background: #fafafa;
  display: flex;
  flex-direction: column;
}
.content_box {
  line-height: 1.8;
  padding: 32rpx;
  img {
    width: 710rpx;
    height: 502rpx;
  }
}
.navbar {
  display: flex;
  align-items: center;
  height: 100rpx;
  background: #fff;
  padding: 0 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}
.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 36rpx;
  font-weight: bold;
}

/deep/ .u-title {
    font-weight: bold;
}
</style>
