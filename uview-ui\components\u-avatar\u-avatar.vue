<template>
	<view class="u-avatar" :style="[wrapStyle]" @tap="click">
		<image
			@error="loadError"
			:style="[imgStyle]"
			class="u-avatar__img"
			v-if="!uText && avatar"
			:src="avatar" 
			:mode="mode"
		></image>
		<text class="u-line-1" v-else-if="uText" :style="{
			fontSize: '38rpx'
		}">{{uText}}</text>
		<slot v-else></slot>
		<view class="u-avatar__sex" v-if="showSex" :class="['u-avatar__sex--' + sexIcon]" :style="[uSexStyle]">
			<u-icon :name="sexIcon" size="20"></u-icon>
		</view>
		<view class="u-avatar__level" v-if="showLevel" :style="[uLevelStyle]">
			<u-icon :name="levelIcon" size="20"></u-icon>
		</view>
	</view>
</template>

<script>
	let base64Avatar = "data:image/png;base64,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";
	/**
	 * avatar 头像
	 * @description 本组件一般用于展示头像的地方，如个人中心，或者评论列表页的用户头像展示等场所。
	 * @tutorial https://www.uviewui.com/components/avatar.html
	 * @property {String} bg-color 背景颜色，一般显示文字时用（默认#ffffff）
	 * @property {String} src 头像路径，如加载失败，将会显示默认头像
	 * @property {String Number} size 头像尺寸，可以为指定字符串(large, default, mini)，或者数值，单位rpx（默认default）
	 * @property {String} mode 显示类型，见上方说明（默认circle）
	 * @property {String} sex-icon 性别图标，man-男，woman-女（默认man）
	 * @property {String} level-icon 等级图标（默认level）
	 * @property {String} sex-bg-color 性别图标背景颜色
	 * @property {String} level-bg-color 等级图标背景颜色
	 * @property {String} show-sex 是否显示性别图标（默认false）
	 * @property {String} show-level 是否显示等级图标（默认false）
	 * @property {String} img-mode 头像图片的裁剪类型，与uni的image组件的mode参数一致，如效果达不到需求，可尝试传widthFix值（默认aspectFill）
	 * @property {String} index 用户传递的标识符值，如果是列表循环，可穿v-for的index值
	 * @event {Function} click 头像被点击
	 * @example <u-avatar :src="src"></u-avatar>
	 */
	export default {
		name: 'u-avatar',
		props: {
			// 背景颜色
			bgColor: {
				type: String,
				default: 'transparent'
			},
			// 头像路径
			src: {
				type: String,
				default: ''
			},
			// 尺寸，large-大，default-中等，mini-小，如果为数值，则单位为rpx
			// 宽度等于高度
			size: {
				type: [String, Number],
				default: 'default'
			},
			// 头像模型，square-带圆角方形，circle-圆形
			mode: {
				type: String,
				default: 'circle'
			},
			// 文字内容
			text: {
				type: String,
				default: ''
			},
			// 图片的裁剪模型
			imgMode: {
				type: String,
				default: 'aspectFill'
			},
			// 标识符
			index: {
				type: [String, Number],
				default: ''
			},
			// 右上角性别角标，man-男，woman-女
			sexIcon: {
				type: String,
				default: 'man'
			},
			// 右下角的等级图标
			levelIcon: {
				type: String,
				default: 'level'
			},
			// 右下角等级图标背景颜色
			levelBgColor: {
				type: String,
				default: ''
			},
			// 右上角性别图标的背景颜色
			sexBgColor: {
				type: String,
				default: ''
			},
			// 是否显示性别图标
			showSex: {
				type: Boolean,
				default: false
			},
			// 是否显示等级图标
			showLevel: {
				type: Boolean,
				default: false
			}
		},
		data() {
			return {
				error: false,
				// 头像的地址，因为如果加载错误，需要赋值为默认图片，props值无法修改，所以需要一个中间值
                avatar: this.src ? this.src : base64Avatar, 
			}
		},
        watch: {
            src(n) {
                // 用户可能会在头像加载失败时，再次修改头像值，所以需要重新赋值
                if(!n) {
					// 如果传入null或者''，或者undefined，显示默认头像
					this.error = true;
				} else {
					this.avatar = n;
					this.error = false;
				}
            }
        },
		computed: {
			wrapStyle() {
				let style = {};
				style.height = this.size == 'large' ? '120rpx' : this.size == 'default' ?
				'90rpx' : this.size == 'mini' ? '70rpx' : this.size + 'rpx';
				style.width = style.height;
				style.flex = `0 0 ${style.height}`;
				style.backgroundColor = this.bgColor;
				style.borderRadius = this.mode == 'circle' ? '500px' : '5px';
				if(this.text) style.padding = `0 6rpx`;
				return style;
			},
			imgStyle() {
				let style = {};
				style.borderRadius = this.mode == 'circle' ? '500px' : '5px';
				return style;
			},
			// 取字符串的第一个字符
			uText() {
				return String(this.text)[0];
			},
			// 性别图标的自定义样式
			uSexStyle() {
				let style = {};
				if(this.sexBgColor) style.backgroundColor = this.sexBgColor;
				return style;
			},
			// 等级图标的自定义样式
			uLevelStyle() {
				let style = {};
				if(this.levelBgColor) style.backgroundColor = this.levelBgColor;
				return style;
			}
		},
		methods: {
			// 图片加载错误时，显示默认头像
			loadError() {
				this.error = true;
                this.avatar = base64Avatar;
			},
			click() {
				this.$emit('click', this.index);
			}
		}
	}
</script>

<style lang="scss" scoped>
	@import "../../libs/css/style.components.scss";

	.u-avatar {
		display: inline-flex;
		align-items: center;
		justify-content: center;
		font-size: 28rpx;
		color: $u-content-color;
		border-radius: 10px;
		position: relative;
		
		&__img {
			width: 100%;
			height: 100%;
		}
		
		&__sex {
			position: absolute;
			width: 32rpx;
			color: #ffffff;
			height: 32rpx;
			display: flex;
			justify-content: center;
			align-items: center;
			border-radius: 100rpx;
			top: 5%;
			z-index: 1;
			right: -7%;
			border: 1px #ffffff solid;
			
			&--man {
				background-color: $u-type-primary;
			}
			
			&--woman {
				background-color: $u-type-error;
			}
			
			&--none {
				background-color: $u-type-warning;
			}
		}
		
		&__level {
			position: absolute;
			width: 32rpx;
			color: #ffffff;
			height: 32rpx;
			display: flex;
			justify-content: center;
			align-items: center;
			border-radius: 100rpx;
			bottom: 5%; 
			z-index: 1;
			right: -7%;
			border: 1px #ffffff solid;
			background-color: $u-type-warning;
		}
	}
</style>
