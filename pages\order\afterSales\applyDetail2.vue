<template>
  <view v-if="serviceDetail" class="service-detail">
    <view class="goods-info-view">
      <view class="goods-info-card">
        <view class="goods-img">
          <u-image
            border-radius="8"
            width="148rpx"
            height="148rpx"
            :src="serviceDetail.goodsImage"
          ></u-image>
        </view>
        <view class="goods-info-main">
          <view class="goods-title">{{ serviceDetail.goodsName }}</view>
          <view class="goods-specs">{{ serviceDetail.goodsSpecs || '白色' }}</view>
          <view class="goods-bottom-row">
            <view class="goods-price">￥{{ serviceDetail.flowPrice | unitPrice }}</view>
            <view class="goods-num">申请售后数量：{{ serviceDetail.num }}</view>
          </view>
        </view>
      </view>
    </view>
  
    
    <view class="address-card">
      <view class="address-title">寄回地址</view>
      <view class="address-row">商家地址：{{ address }}</view>
      <view class="address-row">收货人：{{ consignee }}</view>
      <view class="address-row">联系方式：{{ mobile }}</view>
      <view class="address-row">商家备注：{{ remark }}</view>
    </view>

    <view class="logistics-card">
      <view class="logistics-title">填写物流信息</view>
      <view class="logistics-row">
        <view class="logistics-label">寄回方式</view>
        <view class="logistics-value">快递至第三方卖家</view>
      </view>
      <view class="logistics-row logistics-row-link" @click="showExpressPicker = true">
        <view class="logistics-label">快递公司</view>
        <view class="logistics-value logistics-placeholder" :class="{'selected': selectedExpressCompany}" >
          <view class="logistics-company-text" :class="{'selected': selectedExpressCompany}">{{ selectedExpressCompany || '请选择快递公司' }}</view>
         
          <u-icon name="arrow-right" :color="selectedExpressCompany ? '#333' : 'rgba(153,153,153,0.55)'" />
        </view>
      </view>
      <view class="logistics-row">
        <view class="logistics-label">快递单号</view>
        <u-input class="logistics-input" input-align="right" 
        placeholder="请填写快递单号" v-model="expressNo" @input="onExpressNoInput" 
        placeholder-style="color: rgba(153,153,153,0.55);"/>
      </view>
      <view class="logistics-row">
        <view class="logistics-label">发货时间</view>
        <u-input class="logistics-input"  input-align="right" 
         placeholder="请填写发货时间" v-model="sendTime" 
         placeholder-style="color: rgba(153,153,153,0.55);"/>
      </view>
    </view>
  
    <view style="height:200rpx"></view>
    <view class="bottom-box">
      <view class="bottom-btn">
        提交申请
      </view>
    </view>
    
   <!-- 货物状态弹窗 -->
   <view v-if="showExpressPicker" class="reason-modal-mask">
      <view class="reason-modal">
        <view class="reason-modal-header">
          <view class="reason-modal-title">请选择快递公司</view>
          <u-icon class="close_btn" name="close" size="36" color="#bbb" @click="showExpressPicker = false"></u-icon>
        </view>
        <view class="reason-modal-list">
          <view
            v-for="(item, idx) in goodsStatusList"
            :key="item.value"
            class="reason-modal-item"
            @click="selectGoodsStatus(idx)"
          >
            <view class="reason-modal-label">{{ item.name }}</view>
            <view class="reason-modal-radio">
              <image
                v-if="selectedGoodsStatusIndex === idx"
                src="/static/gou.png"
                mode="scaleToFill"
              />
              <view v-else class="radio-placeholder"></view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import {
  getServiceDetail,
  getStoreAfterSaleAddress,
  getAfterSaleLog,
  getAfterSaleReason,
} from "@/api/after-sale.js";
export default {
  data() {
    return {
      reason: "", //申请原因
      serviceTypeList: {
        // 售后类型
        CANCEL: "取消",
        RETURN_GOODS: "退货",
        EXCHANGE_GOODS: "换货",
        RETURN_MONEY: "退款",
      },
      serviceDetail: {}, // 售后详情
      logs: [], //日志
      goodsList: [], //商品列表
      storeAfterSaleAddress: {}, //售后地址
      refundShow: false, //退款开关
      accountShow: false, //账户显示
      bankShow: false, //银行显示
      sn: "", //订单sn
      expressNo: '',
      sendTime: '',
      address: '',
      consignee: '',
      mobile: '',
      remark: '',
      goodsStatusList: [
        { value: 1,name: '顺丰速运', code: 'SF' },
        { value: 2,name: '中通快递', code: 'ZTO' },
        { value: 3,name: '圆通速递', code: 'YTO' },
        { value: 4,name: '申通快递', code: 'STO' },
        { value: 5,name: '韵达快递', code: 'YD' },
        // ...可自行扩展
      ],
      
      showExpressPicker: false,
      selectedExpressCompany: '', // 选中的快递公司名
      selectedGoodsStatusIndex: null, // 选中的下标
    };
  },
  onLoad(options) {
    uni.setNavigationBarTitle({
      title: "服务单详情",
    });
    this.sn = options.sn;
    this.loadDetail();
    this.getAddress();
    this.getLog(options.sn);
  },
  filters: {
    /**
     * 售后状态信息
     */
    statusFilter(val) {
      switch (val) {
        case "APPLY":
          return "售后服务申请成功，等待商家审核";
        case "PASS":
          return "售后服务申请审核通过";
        case "REFUSE":
          return "售后服务申请已被商家拒绝，如有疑问请及时联系商家";
        case "FULL_COURIER":
          return "申请售后的商品已经寄出，等待商家收货";
        case "STOCK_IN":
          return "商家已将售后商品入库";
        case "WAIT_FOR_MANUAL":
          return "等待平台进行人工退款";
        case "REFUNDING":
          return "商家退款中，请您耐心等待";
        case "COMPLETED":
          return "售后服务已完成，感谢您的支持";
        case "ERROR_EXCEPTION":
          return "系统生成新订单异常，等待商家手动创建新订单";
        case "CLOSED":
          return "售后服务已关闭";
        case "WAIT_REFUND":
          return "等待平台进行退款";
        default:
          return "";
      }
    },

    /**
     * 退款信息
     */
    refundWayFilter(val) {
      switch (val) {
        case "OFFLINE":
          return "账户退款";
        case "OFFLINE":
          return "线下退款";
        case "ORIGINAL":
          return "原路退回";
        default:
          return "";
      }
    },
    /**
     * 账户信息
     */
    accountTypeFilter(val) {
      switch (val) {
        case "WEIXINPAY":
          return "微信";
        case "ALIPAY":
          return "支付宝";
        case "BANK_TRANSFER":
          return "银行卡";
        default:
          return "";
      }
    },
  },
  methods: {
    /**
     * 点击图片放大或保存
     */
    preview(urls, index) {
      uni.previewImage({
        current: index,
        urls: urls,
        longPressActions: {
          itemList: ["保存图片"],
          success: function (data) {},
          fail: function (err) {},
        },
      });
    },

    /**
     * 获取地址信息
     */
    getAddress() {
      getStoreAfterSaleAddress(this.sn).then((res) => {
        if (res.data.success) {
          this.storeAfterSaleAddress = res.data.result;
          this.address = res.data.result.salesConsigneeAddressPath || '';
          this.consignee = res.data.result.salesConsigneeName || '';
          this.mobile = res.data.result.salesConsigneeMobile || '';
          this.remark = res.data.result.remark || '';
        }
      });
    },

    /**
     * 获取日志
     */
    getLog(sn) {
      getAfterSaleLog(sn).then((res) => {
        this.logs = res.data.result;
      });
    },

    /**
     * 获取申请原因
     */
    getReasonList(serviceType) {
      getAfterSaleReason(serviceType).then((res) => {
        if (res.data.success) {
          // 1357583466371219456
          this.reason = this.serviceDetail.reason;
        }
      });
    },

    /**
     * 初始化详情
     */
    loadDetail() {
      // uni.showLoading({
      //   title: "加载中",
      // });
      getServiceDetail(this.sn).then((res) => {
        if (this.$store.state.isShowToast) {
          // uni.hideLoading();
        }
        this.serviceDetail = res.data.result;
        if (
          this.serviceDetail.serviceType == "RETURN_GOODS" ||
          this.serviceDetail.serviceType === "RETURN_MONEY"
        ) {
          this.refundShow = true;
        }

        this.accountShow =
          (this.serviceDetail.serviceType === "RETURN_GOODS" ||
            this.serviceDetail.serviceType === "ORDER_CANCEL") &&
          this.serviceDetail.refundWay === "OFFLINE";

        this.bankShow =
          this.serviceDetail.accountType === "BANK_TRANSFER" &&
          this.serviceDetail.refundWay === "OFFLINE" &&
          ((this.serviceDetail.serviceType === "RETURN_GOODS") |
            (this.serviceDetail.serviceType === "ORDER_CANCEL") ||
            this.serviceDetail.serviceType === "RETURN_MONEY");

        this.getReasonList(this.serviceDetail.serviceType);
      });
    },

    /**
     * 访问商品详情
     */
    navgiateToGoodsDetail(item) {
      uni.navigateTo({
        url: `/pages/product/goods?id=${item.id}&goodsId=${item.goodsId}`,
      });
    },

    /**
     * 进度
     */
    onProgress() {
      uni.navigateTo({
        url: `./applyProgress?sn=${
          this.serviceDetail.sn
        }&createTime=${encodeURIComponent(this.serviceDetail.createTime)}
         &logs=${encodeURIComponent(JSON.stringify(this.logs))}&serviceStatus=${
          this.serviceDetail.serviceStatus
        }`,
      });
    },

    getAfterSaleSn(message) {
      if (!message) return "";
      const match = message.match(/\[([A-Za-z0-9]+)\]/);
      return match ? match[1] : "";
    },

    onExpressNoInput(e) {
      const value = typeof e === 'string' ? e : e.detail.value;
      this.expressNo = value;
      // 这里可以调用快递单号识别接口，识别后自动高亮弹窗中的公司
      // 这里只做本地模拟：如果单号以 1 开头，选顺丰，以 2 开头选中通，以此类推
      if (value.length >= 8) {
        let idx = null;
        if (value.startsWith('1')) idx = 0;
        else if (value.startsWith('2')) idx = 1;
        else if (value.startsWith('3')) idx = 2;
        else if (value.startsWith('4')) idx = 3;
        else if (value.startsWith('5')) idx = 4;
        this.selectedGoodsStatusIndex = idx;
        if (idx !== null) {
          this.selectedExpressCompany = this.goodsStatusList[idx].name;
        } else {
          this.selectedExpressCompany = '';
        }
      } else {
        this.selectedGoodsStatusIndex = null;
        this.selectedExpressCompany = '';
      }
    },
    selectGoodsStatus(idx) {
      this.selectedGoodsStatusIndex = idx;
      this.selectedExpressCompany = this.goodsStatusList[idx].name;
      this.showExpressPicker = false;
    },
  },
};
</script>

<style lang="scss" scoped>
page,
.content {
  background: $page-color-base;
  height: 100%;
}
.after-sales-goods-detail-view {
  background-color: #fff;
  .header {
    background-color: #f7f7f7;
    color: #999999;
    font-size: 22rpx;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    line-height: 70rpx;
    .header-text {
      background-color: #999999;
      padding: 10rpx 30rpx;
      border-radius: 50rpx;
    }
    .seller-name {
      color: $main-color;
    }
  }
  .apply-info-view {
    // background: $page-color-base;
    width: 750rpx;
    height: 248rpx;
    background: linear-gradient(180deg, #ff6634 0%, rgba(255, 102, 52, 0) 100%);
    border-radius: 0rpx 0rpx 0rpx 0rpx;
  }

  .goods-item-view {
    display: flex;
    flex-direction: row;
    padding: 20rpx 30rpx;
    background-color: #eef1f2;

    .goods-num {
      width: 60rpx;
      color: $main-color;
    }
  }
}
.apply-detail-view {
  background-color: #fff;
  padding: 20rpx;
  color: #666666;
  width: 686rpx;
  margin: 20rpx auto;
  border-radius: 20rpx;
  .detail-item {
    padding: 12rpx;
    display: flex;
    flex-direction: row;
    align-items: center;
    font-size: 24rpx;
    .title {
      padding-right: 10rpx;
      // width: 140rpx;
    }
    .value {
      // padding-left: 40rpx;
    }
  }
}

.log-box-bottom {
  height: 120rpx;
  flex-direction: column;
  background-color: rgb(247, 247, 247);
}
.log-box-top {
  width: 686rpx;
  background: #ffffff;
  border-radius: 20rpx;
  margin: 30rpx auto 0;
  position: relative;
  z-index: 19;
  padding: 32rpx;
  box-sizing: border-box;
  display: flex;
  .top01 {
    width: 90%;
    font-family: PingFangSC-Regular;
    font-weight: 500;
    font-size: 28rpx;
    color: #333333;
    overflow: hidden;
    text-overflow: ellipsis;
    overflow-wrap: break-word;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    white-space: normal;

    .log-first-show {
      flex-direction: row;
      font-family: PingFangSC-Regular;
      font-weight: 400;
      font-size: 24rpx;
      color: #666666;
      margin-top: 20rpx;
    }
  }
}
.status-val {
  padding: 40rpx 0 0 60rpx;
  font-weight: 500;
  font-size: 36rpx;
  color: #ffffff;
}
.status-tip {
  font-size: 24rpx;
}
.info-box {
  padding-right: 40rpx 0rpx;
  background-color: #eef1f2;
}
.service-detail {
  background: #f7f7f7;
}
.goods-info {
  padding-left: 30rpx;
  flex: 1;
  background: #fff;
  display: flex;
  .goods-title {
    margin-bottom: 10rpx;
    font-size: 28rpx;
    color: $font-color-dark;
  }
  .goods-specs {
    font-size: 24rpx;
    margin-bottom: 10rpx;
    color: #cccccc;
  }
  .goods-price {
    display: flex;
    justify-content: space-between;
    font-size: 24rpx;
    color: #999999;
  }
  .price {
    color: $light-color;
  }
}
.goods-info-card {
  width: 686rpx;
  margin: 0 auto;
  background: #fff;
  border-radius: 20rpx;
  box-shadow: 0 8rpx 32rpx 0 rgba(0,0,0,0.06);
  display: flex;
  align-items: center;
  padding: 24rpx;
}
.goods-img {
  width: 148rpx;
  height: 148rpx;
  border-radius: 8rpx;
  overflow: hidden;
  flex-shrink: 0;
}
.goods-info-main {
  flex: 1;
  margin-left: 20rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.goods-title {
  font-size: 32rpx;
  color: #333;
  font-weight: 400;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  white-space: normal;
  display: -webkit-box;

}
.goods-specs {
  font-size: 22rpx;
  color: #bbb;
  margin-bottom: 12rpx;
}
.goods-bottom-row {
  display: flex;
  align-items: flex-end;
  justify-content: space-between;
}
.goods-price {
  font-weight: 500;
  font-size: 28rpx;
  color: #FF5134;
}
.goods-num {
  font-weight: 400;
  font-size: 20rpx;
  color: #666666;
}
.goods-info-view {
  height: 248rpx;
  background: linear-gradient( 180deg, #FF6634 0%, rgba(255,102,52,0) 100%);
  padding-top: 20rpx;
}
.address-card {
  background: #fff;
  border-radius: 20rpx;
  padding: 32rpx 28rpx 28rpx 28rpx;
  margin: 24rpx auto;
  width: 90%;
  box-sizing: border-box;
}
.address-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}
.address-row {
  font-size: 26rpx;
  color: #666;
  line-height: 44rpx;
  margin-bottom: 20rpx;
}
.logistics-card {
  background: #fff;
  border-radius: 20rpx;
  padding: 32rpx 28rpx 28rpx 28rpx;
  margin: 24rpx auto;
  width: 90%;
  box-sizing: border-box;
}
.logistics-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 32rpx;
}
.logistics-row {
  display: flex;
  flex-direction: row;
  align-items: center;
  font-size: 26rpx;
  color: #333;
  padding: 32rpx 0;
  border-bottom: 1rpx solid #f2f2f2;
}
.logistics-row:last-child {
  border-bottom: none;
  padding-bottom: 0;
  padding-top: 32rpx;
}
.logistics-label {
  flex: 0 0 180rpx;
  color: #666;
}
.logistics-value {
  flex: 1;
  text-align: right;
  color: #333;
}
.logistics-placeholder {
  color: rgba(153,153,153,0.55);
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.arrow {
  font-size: 32rpx;
  color: #ccc;
  margin-left: 8rpx;
}
.logistics-row-link {
  cursor: pointer;
  transition: background 0.2s;
}
.logistics-row-link:active {
  background: #f5f5f5;
}
.logistics-value.logistics-placeholder {
  color: rgba(153,153,153,0.55);
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.logistics-company-text {
  display: inline-block;
  max-width: 70%;
  text-align: right;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: rgba(153,153,153,0.55);
}
.logistics-company-text.selected {
  color: #333;
}
.logistics-input {
  flex: 1;
  border: none;
  outline: none;
  background: transparent;
  text-align: right;
  font-size: 26rpx;
  color: #333;
}
.logistics-input::placeholder {
  color: rgba(153,153,153,0.55) !important;
  font-size: 26rpx;
}
.reason-modal-mask {
  position: fixed;
  left: 0; top: 0; right: 0; bottom: 0;
  background: rgba(0,0,0,0.3);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}
.reason-modal {
  background: #fff;
  border-radius: 20rpx;
  width: 600rpx;
  max-width: 90vw;
  padding-bottom: 20rpx;
}
.reason-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx 32rpx 0 32rpx;
}
.reason-modal-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
}
.close_btn {
  cursor: pointer;
}
.reason-modal-list {
  padding: 0 32rpx;
}
.reason-modal-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 28rpx 0;
  border-bottom: 1rpx solid #f2f2f2;
}
.reason-modal-item:last-child {
  border-bottom: none;
}
.reason-modal-label {
  font-size: 28rpx;
  color: #333;
}
.reason-modal-radio {
  width: 36rpx;
  height: 36rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.radio-placeholder {
  width: 36rpx;
  height: 36rpx;
  border-radius: 50%;
  border: 1rpx solid #ccc;
  background: #fff;
}
.reason-modal-mask {
  position: fixed;
  z-index: 9999;
  left: 0; top: 0; right: 0; bottom: 0;
  background: rgba(0,0,0,0.4);
  display: flex;
  align-items: flex-end;
  justify-content: center;
}
.reason-modal {
  width: 100vw;
  max-width: 100vw;
  background: #fff;
  border-radius: 24rpx 24rpx 0 0;
  padding-bottom: 32rpx;
  animation: modalIn .2s;
}
@keyframes modalIn {
  from { transform: translateY(100%);}
  to { transform: translateY(0);}
}
.reason-modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 32rpx 0 32rpx;
  position: relative;
  text-align: center;
}
.close_btn {
  position: absolute;
  top: 40rpx;
  right: 40rpx;
}
.reason-modal-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  text-align: center;
  flex: 1;
}
.reason-modal-list {
  max-height: 600rpx;
  overflow-y: auto;
  margin: 24rpx 0 0 0;
}
.reason-modal-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 28rpx 32rpx;
  font-size: 30rpx;
  color: #333;
  // border-bottom: 1px solid #f5f5f5;
  cursor: pointer;
  background: #fff;
  &.selected {
    color: #ff5000;
    background: #fff7f0;
    font-weight: 500;
  }
}
.reason-modal-label {
  flex: 1;
}
.reason-modal-radio {
  margin-left: 24rpx;
  image {
    width: 32rpx;
    height: 32rpx;
  }
  view {
    width: 32rpx;
    height: 32rpx;
    border-radius: 50%;
    border: 1rpx solid #999999;
  }
}
.radio-outer {
  width: 36rpx;
  height: 36rpx;
  border-radius: 50%;
  border: 2rpx solid #ff5000;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fff;
}
.radio-check {
  font-size: 28rpx;
  color: #ff5000;
  font-weight: bold;
  line-height: 1;
}

.leave-modal-mask {
  position: fixed;
  left: 0; top: 0; right: 0; bottom: 0;
  background: rgba(0,0,0,0.45);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
}
.leave-modal-box {
  background: #fff;
  border-radius: 18rpx;
  width: 520rpx;
  padding: 48rpx 0 0 0;
  box-shadow: 0 8rpx 32rpx 0 rgba(0,0,0,0.10);
  text-align: center;
}
.leave-modal-title {
  font-size: 32rpx;
  color: #222;
  font-weight: 500;
  margin-bottom: 18rpx;
}
.leave-modal-desc {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 40rpx;
  padding: 0 40rpx;
}
.leave-modal-btns {
  display: flex;
  border-top: 1rpx solid #eee;
  height: 96rpx;
  margin-top: 0;
}
.leave-modal-btn {
  flex: 1;
  font-size: 32rpx;
  line-height: 96rpx;
  cursor: pointer;
}
.leave-modal-btn.cancel {
  color: #999;
  border-right: 1rpx solid #eee;
}
.leave-modal-btn.confirm {
  color: #FF5134;
  font-weight: 500;
}
.bottom-box {
  width: 100%;
  height: 120rpx;
  background: #fff;
  position: fixed;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
}
.bottom-btn {
  width: 670rpx;
  height: 100rpx;
  background: linear-gradient( 90deg, #FF5235 0%, #FF9500 100%);
  border-radius: 86rpx;
  margin: 10rpx auto;
  font-weight: 400;
  font-size: 32rpx;
  color: #FFFFFF;
  text-align: center;
  line-height: 100rpx;
}
</style>
