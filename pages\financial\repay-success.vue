<template>
  <view class="repay-success-wrapper">
    <u-navbar back-text="返回" :is-back="false" title="还款结果"></u-navbar>
    <view class="repay-success-content">
      <image
        :src="statusImg"
        mode="scaleToFill"
        class="success-image"
      />
      <view class="success-amount">
        {{ amount }}元{{ isSuccess ? '还款成功' : '还款失败' }}
      </view>
      <view :class="['success-btn', isSuccess ? '' : 'fail']" @click="goBack">
        {{ isSuccess ? '完成' : '返回重试' }}
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      amount: '436.10',
      isSuccess: true,
    };
  },
  computed: {
    statusImg() {
      return this.isSuccess
        ? '/static/img/payment_success.png'
        : '/static/img/fail.png';
    },
  },
  onLoad(options) {
    if (options && options.amount) {
      this.amount = options.amount;
    }
    if (options && options.type) {
      this.isSuccess = options.type === 'success';
    }
  },
  methods: {
    goBack() {
      if (this.isSuccess) {
        uni.switchTab({ url: '/pages/tabbar/home/<USER>' });
      } else {
        uni.navigateBack();
      }
    },
  },
};
</script>

<style scoped lang="scss">
.repay-success-wrapper {
  background: #fff;
  min-height: 100vh;
}
.repay-success-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 144rpx;
}
.success-image {
  width: 260rpx;
  height: 260rpx;
  margin-bottom: 40rpx;
}
.success-amount {
  font-weight: 400;
  font-size: 36rpx;
  color: #333;
  margin-bottom: 120rpx;
}
.success-btn {
  width: 670rpx;
  height: 100rpx;
  background: linear-gradient(90deg, #ff5235 0%, #ff9500 100%);
  border-radius: 86rpx;
  font-size: 32rpx;
  color: #fff;
  text-align: center;
  line-height: 100rpx;
  position: fixed;
  bottom: 80rpx;
  left: 50%;
  transform: translateX(-50%);
  &.fail {
    background: linear-gradient(90deg, #b7b7b7 0%, #ff4d4f 100%);
  }
}
</style> 