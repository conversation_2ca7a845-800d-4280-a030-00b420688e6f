<template>
  <view class="securityCenter">
    <!-- <u-cell-group>
      <u-cell-item title="验证密码" @click="navigateTo('/pages/mine/set/securityCenter/editLoginPassword')"></u-cell-item>
      <u-cell-item title="验证手机号" @click="navigateTo('/pages/mine/set/securityCenter/editPassword')"></u-cell-item>
    </u-cell-group> -->
    <view class="card">
      <u-cell-item title="验证密码" @click="navigateTo('/pages/mine/set/securityCenter/editLoginPassword')" />
      <!-- <u-cell-item title="支付密码" value="修改支付密码" @click="navigateTo('/pages/mine/set/securityCenter/updatePayPwdTab')" /> -->
      <u-cell-item title="验证手机号" :border-bottom="false" @click="navigateTo('/pages/mine/set/securityCenter/editPassword')" />
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      mobile: "", //存储手机号
    };
  },

  methods: {
    navigateTo(url) {
      uni.navigateTo({
        url: url,
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.securityCenter {
  .u-cell {
    line-height: normal;
  }
}
.card {
  margin: 40rpx 24rpx 0 24rpx;
  border-radius: 16rpx;
  background: #fff;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.03);
  overflow: hidden;
}
/deep/ .u-cell__value {
  color: #cccccc !important;
}
</style>
