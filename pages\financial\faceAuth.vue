<template>
  <view class="face-auth-page">
    <view class="auth-section">
      <view class="auth-avatar-box">
        <image
          src="/static/img/avatar-auth.png"
          mode="aspectFit"
          class="auth-avatar"
        />
      </view>
      <view class="auth-desc-box">
        <view class="auth-desc-title">您知悉并同意应用提供者：</view>
        <view class="auth-desc-list">
          <view>• 收集、使用您本人的身份信息和人脸图像</view>
          <view>• 向合法数据持有者核验您的身份信息</view>
          <view>• 本操作数据仅用于身份核实，安全可靠</view>
        </view>
      </view>
      <view class="auth-line-box">
        <view class="auth-checkbox-box">
          <u-checkbox
            v-model="checked"
            shape="circle"
            active-color="#ff6b35"
            size="26"
          ></u-checkbox>
          <text class="auth-link"
            >上述为个人敏感信息，您知悉并同意<text
              class="auth-protocol"
              @click="goToProtocol"
              >《个人信息授权协议》</text
            >，如拒绝，将无法使用本功能。</text
          >
        </view>
        <view class="auth-btn-box">
          <u-button
            type="primary"
            class="auth-btn"
            @click="continueAuth"
            >同意授权并继续</u-button
          >
        </view>
      </view>
    </view>
    <u-popup v-model="showCreditModal" mode="bottom" border-radius="16" :closeable="true">
        <view style="padding: 60rpx 40rpx; 0">
          <view class="credit-modal-header">
                <text class="credit-modal-title">{{ title }}</text>
                <!-- <u-icon name="close" size="36" color="#bbb" class="credit-modal-close" @click="closeCreditModal" /> -->
          </view>
          <!-- <u-tabs :list="tbaList"  :current="currentTitle" @change="tabChange"
             active-color="#333" inactive-color="#666" :bar-style="barStyle"
            ></u-tabs> -->
            <view class="credit-modal">
             
              <scroll-view scroll-y class="credit-modal-content">
                <view class="credit-modal-text">
                  <u-parse :html="content"  :tag-style="style"></u-parse>
                </view>
              </scroll-view>
              <button class="credit-modal-btn" @click="closeCreditModal">同意协议并申请</button>
            </view>
        </view>
        
      </u-popup>
  </view>
</template>

<script>
import { getFaceParam } from '@/api/safe.js';
// #ifdef APP-PLUS
const ocr  = uni.requireNativePlugin('DC-WBOCRService');
	//如果只使用人脸插件，请注释上面的OCR插件，同时依赖下面的Normal与Face插件
	// const normal = uni.requireNativePlugin('DC-WBNormal');
const face = uni.requireNativePlugin('DC-WBFaceService');
// #endif

import { getFaceResult, getAuthAssessmentAmount } from '@/api/safe.js';
import { getPrivacyDet } from '@/api/article.js';

export default {
  data() {
    return {
      showAuth: true,
      checked: true,
      faceResult: "",
      faceImg: "",
      userInfo: {},
      showCreditModal: false,
      title: '',
      content: '',
      creditModalContent: `
          1. 获取您的人脸信息，用于身份核验。
          2. 获取您的手机号码，用于登录和授权。
      `,
      style: {
        img: "width: 710rpx;height:502rpx",
         
      },
    };
  },
  methods: {
    continueAuth() {
      if (!this.checked) {
        return uni.showToast({ title: "请勾选同意协议", icon: "none" });
      }
      this.showAuth = false;
      this.startWbFaceVerifyService();
    },
    goToProtocol() {
      // uni.navigateTo({ url: "/pages/protocol/personalInfoAuth" });
      this.showCreditModal = true;
      this.getPrivacyList('1950029358716051458');
    },
    async getPrivacyList(val){
      const res = await getPrivacyDet(val);
      console.log(res);
      if (res.statusCode === 200) {
        this.title = res.data.result.title
        this.content = res.data.result.content
      }
    },
    takePhoto() {
      const ctx = uni.createCameraContext();
      ctx.takePhoto({
        quality: "high",
        success: (res) => {
          this.faceImg = res.tempImagePath;
          uni.showLoading({ title: "识别中..." });
          setTimeout(() => {
            uni.hideLoading();
            this.faceResult = "识别成功，身份验证通过";
          }, 1500);
        },
        fail: () => {
          uni.showToast({ title: "拍照失败", icon: "none" });
        },
      });
    },
    goBack() {
      uni.navigateBack();
    },
    closeCreditModal() {
      this.showCreditModal = false;
      // 自动勾选服务授权并登录
      if (!this.checked) {
        this.checked = true;
      }
      // if (this.showAuthModal) {
      //   this.handleAuthLogin();
      // }
    },
    startWbFaceVerifyService() {
      getFaceParam().then(res => {
        const data = res.data.result;
        this.userInfo = data;
        console.log('组装参数并调用SDK',data);
        // 组装参数并调用SDK
        face.startWbFaceVerifyService({
          apiVersion: data.apiVersion || "1.0.0",
          appId: data.appId,
          nonce: data.nonce,
          userId: data.userId,
          sign: data.sign,
          orderNo: data.orderNo,
          licence: data.licence,
          faceId: data.faceId,
          compareType: "0", // 仅活体检测
          sdkConfig: {
            showSuccessPage: false,
            showFailurePage: false,
            recordVideo: false,
            checkVideo: false,
            playVoice: false,
            theme: '1',
            customerTipsLoc: '0',
            customerTipsInLive: '',
            customerTipsInUpload: '',
            customerLongTip: '',
            isEnableLog: true,
            windowLevel: '1',
            manualCookie: true,
            useWindowSecene: false,
          }
        }, result => {
          let scene = result.scene;
          if (scene == 'wb_face_callback_login_failure') {
            let error = result.res.error;
            let tip = '核验失败，请重新尝试';
            console.log(tip);
            uni.showToast({
              icon: 'none',
              title: tip
            });
            return;
          }
          if (scene == "wb_face_callback_verify_result") {
            let res = result.res;
            console.log(res);
            let success = res.success;
            if (success) {
              // 认证成功后的逻辑
              getFaceResult(res.orderNo).then(faceResultRes => {
                console.log('后端人脸核验结果', faceResultRes);
                // 可根据需要将结果保存到data或做进一步处理
                if (faceResultRes.data.code == 200) {
                  console.log('后端人脸成功');
                  uni.showToast({
                    icon: 'none',
                    title: '认证成功'
                  });

                  // 认证成功后调用评估额度接口
                  uni.showLoading({
                    title: '评估中...',
                    mask: true
                  });

                  getAuthAssessmentAmount().then(assessmentRes => {
                    console.log('评估额度结果', assessmentRes);
                    uni.hideLoading();

                    // 根据返回的数据结构判断成功或失败
                    if (assessmentRes.data && assessmentRes.data.success && assessmentRes.data.code === 200) {
                      // 评估成功，跳转到审核页
                      uni.redirectTo({
                        url: '/pages/financial/reviewing'
                      });
                    } else {
                      // 评估失败，跳转到失败页
                      uni.redirectTo({
                        url: '/pages/financial/assessmentFailed'
                      });
                    }
                  }).catch(assessmentErr => {
                    console.error('评估额度失败', assessmentErr);
                    uni.hideLoading();
                    // 网络请求失败，跳转到失败页
                    uni.redirectTo({
                      url: '/pages/financial/assessmentFailed'
                    });
                  });
                }
              }).catch(err => {
                console.error('获取后端人脸核验结果失败', err);
                uni.showToast({
                  icon: 'none',
                  title: '后端核验失败'
                });
              });
            } else {
              let error = res.error;
              let domain = error.domain;
              if (domain == "WBFaceErrorDomainCompareServer") {
                uni.showToast({
                  icon: 'none',
                  title: "对比失败，liveRate=" + res.liveRate + "; similarity=" + res.similarity + ";sign=" + res.sign
                });
                return;
              }
              uni.showToast({
                icon: 'none',
                title: JSON.stringify(error)
              });
            }
          }
        });
      }).catch(err => {
        uni.showToast({
          icon: 'none',
          title: '获取人脸参数失败'
        });
      });
    },
  },
};
</script>

<style lang="scss" scoped>
@import "@/uni.scss";
page {
  background: #fff;
}
.face-auth-page {
  background: #fff;
}
.auth-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 80rpx 32rpx 0 32rpx;
}
.auth-avatar-box {
  margin-bottom: 32rpx;
}
.auth-avatar {
  width: 264rpx;
  height: 264rpx;
  border-radius: 50%;
  background: #e6f0fa;
}
.auth-desc-box {
  background: #fff;
  padding: 32rpx;
  margin-bottom: 40rpx;
  width: 686rpx;
  height: 272rpx;
  background: #f7f7f7;
  border-radius: 20rpx;
}
.auth-desc-title {
  font-weight: 500;
  font-size: 32rpx;
  color: #333333;
  margin-bottom: 20rpx;
}
.auth-desc-list {
  font-weight: 400;
  font-size: 28rpx;
  color: #666666;
  line-height: 1.2;
  view {
    margin-bottom: 12rpx;
  }
}
.auth-line-box {
  position: fixed;
  bottom: 0;
  width: 746rpx;
  height: 240rpx;
  background: #ffffff;
  box-shadow: 0rpx -4rpx 8rpx 0rpx rgba(0, 0, 0, 0.04);
  padding: 32rpx 36rpx 22rpx 40rpx;
}
.auth-checkbox-box {
  font-weight: 400;
  font-size: 24rpx;
  color: #666666;
  .auth-link {
    margin-left: -20rpx;
    line-height: 1.4;
  }
}
.auth-protocol {
  color: #ff5134;
  margin-left: 8rpx !important;
}
.auth-btn-box {
  width: 100%;
  max-width: 600rpx;
  margin-top: 20rpx;
}
.auth-btn {
  width: 670rpx;
  height: 100rpx;
  background: linear-gradient(90deg, #ff5235 0%, #ff9500 100%);
  border-radius: 86rpx 86rpx 86rpx 86rpx;
}
.title-bar_box {
  width: 750rpx;
  background: linear-gradient(180deg, #fbeae1 0%, rgba(251, 234, 225, 0) 100%);
  border-radius: 0rpx 0rpx 0rpx 0rpx;
  padding: 34rpx 32rpx;
}
.title-bar {
  .main-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #444444;
  }
}
.desc {
  margin: 6rpx 0 36rpx 0;
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #666666;
  image {
    width: 32rpx;
    height: 32rpx;
  }
}
.face-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 40rpx;
}
.camera-box {
  width: 600rpx;
  height: 600rpx;
  border-radius: 20rpx;
  overflow: hidden;
  background: #000;
  display: flex;
  align-items: center;
  justify-content: center;
}
.face-btns {
  margin-top: 32rpx;
  width: 600rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.face-btn {
  width: 100%;
  height: 88rpx;
  font-size: 32rpx;
  border-radius: 44rpx;
}
.face-result {
  margin-top: 32rpx;
  color: #ff6b35;
  font-size: 28rpx;
  text-align: center;
}
.face-result-title {
  font-weight: bold;
}
.face-img-preview {
  display: flex;
  justify-content: center;
}

.credit-modal-header {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  margin-bottom: 40rpx;
  // padding: 32rpx 0 0 0;
}
.credit-modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #222;
}
.credit-modal-close {
  position: absolute;
  right: 32rpx;
  top: 32rpx;
}
.credit-modal-content {
  width: 100%;
  max-height: 716rpx;
  min-height: 400rpx;
  margin: 24rpx 0 0 0;
  background: #F7F7F7;
  border-radius: 12rpx;
  overflow-y: auto;
}
.credit-modal-text {
  font-size: 24rpx;
  color: #333;
  line-height: 1.7;
  padding: 0 32rpx;
}
.credit-modal-btn {
  width: 670rpx;
  height: 100rpx;
  background: linear-gradient(90deg, #ff5235 0%, #ff9500 100%);
  color: #fff;
  font-size: 30rpx;
  border: none;
  border-radius: 78rpx;
  margin-top: 32rpx;
  font-weight: bold;
  line-height: 100rpx;
}
.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100vh;
}
</style>
