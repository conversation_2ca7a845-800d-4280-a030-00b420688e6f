import queryParams from '@/uview-ui/libs/function/queryParams.js'

export function showModal(options) {
    try {
        uni.navigateTo({
            url: '/pages/popup/showModal' + queryParams({
                title: options.title,
                content: options.content
            })
        })
        uni.$once('myModelResult', result => {
            if (options.success && typeof options.success === 'function') {
                options.success.call(null, result)
            }
        })
    } catch (err) {
        if (options.fail && typeof options.fail === 'function') {
            options.fail.call(null, err)
        }
    } finally {
        if (options.complete && typeof options.complete === 'function') {
            options.complete.call(null, {})
        }
    }
    // uni.showModal(options)
  
}