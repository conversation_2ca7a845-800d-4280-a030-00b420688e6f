<template>
  <view class="address">

    <u-empty class="empty" v-if="addressList.length == 0" text="暂无收货地址" mode="address"></u-empty>
    <view class="list" v-else>
      <view class="address-item-wrapper" v-for="item in addressList" :key="item.id">
        <u-swipe-action
          :options="[{ text: '删除', style: { backgroundColor: '#FF4E3E', color: '#fff', fontSize: '32rpx' } }]"
          @click="removeAddress(item.id)"
        >
          <view class="item swipe-item" style="border:none;">
            <view class="basic" @click="selectAddressData(item)">
              <view style="display: flex; align-items: center;">
                <text v-if="item.isDefault" class="default-label">默认</text>
                <text class="name">{{ item.name }}</text>
                <text class="mobile">{{ item.mobile }}</text>
              </view>
              <view class="region-text">
                <text>
                  <span v-if="item.consigneeAddressPath[0]">{{item.consigneeAddressPath[0]}}</span>
                  <span v-if="item.consigneeAddressPath[1]">{{item.consigneeAddressPath[1]}}</span>
                  <span v-if="item.consigneeAddressPath[2]">{{item.consigneeAddressPath[2]}}</span>
                  <span v-if="item.consigneeAddressPath[3]">{{item.consigneeAddressPath[3]}}</span>
                  <span>{{ item.detail }}</span>
                </text>
              </view>
            </view>
            <view class="edit">
              <view class="relative">
                <view class="alifont icon-bianji-copy"></view>
                <text class="edit-btn" @click.stop="addAddress(item.id)">编辑</text>
              </view>
            </view>
          </view>
        </u-swipe-action>
      </view>
    </view>
    <button type="default" class="btn" @click="addAddress('')">
      添加新收货地址
    </button>
    <u-action-sheet :list="removeList" :tips="tips" v-model="showAction" @click="deleteAddressMessage"></u-action-sheet>
  </view>
</template>

<script>
import * as API_Trade from "@/api/trade";
import * as API_Address from "@/api/address.js";
export default {
  data() {
    return {
      addressList: [], //地址列表
      showAction: false, //是否显示下栏框
      removeList: [
        {
          text: "确定",
        },
      ],
      tips: {
        text: "确定要删除该收货人信息吗？",
      },
      removeId: "", //删除的地址id
      routerVal: "",
      params: {
        pageNumber: 1,
        pageSize: 1000,
      },
    };
  },
  onPullDownRefresh() {
    //下拉刷新
    this.addressList = [];
    this.getAddressList();
  },
  onLoad: function (val) {
    this.routerVal = val;
  },
  onShow() {
    this.addressList = [];
    this.getAddressList();
  },
  onHide() {},
  methods: {
    async selectAddressData(val) {
      await API_Trade.setAddressId(val.id, this.routerVal.way);

      uni.navigateBack({
        delta: 1,
      });
    },
    //获取地址列表
    getAddressList() {
      uni.showLoading();

      API_Address.getAddressList(
        this.params.pageNumber,
        this.params.pageSize
      ).then((res) => {
        res.data.result.records.forEach((item) => {
          item.consigneeAddressPath = item.consigneeAddressPath.split(",");
        });
        this.addressList = res.data.result.records;
        console.log(this.addressList);

         if (this.$store.state.isShowToast){ uni.hideLoading() };
      });
    },
    //删除地址
    removeAddress(id) {
      this.removeId = id;
      this.showAction = true;
    },
    deleteAddressMessage() {
      API_Address.deleteAddress(this.removeId).then((res) => {
        if (res.statusCode == 200) {
          uni.showToast({
            icon: "none",
            title: "删除成功",
          });
          this.getAddressList();
        } else {
          uni.showToast({
            icon: "none",
            title: res.data.message,
            duration: 2000,
          });
        }
      });
    },
    //新建。编辑地址
    addAddress(id) {
      if (id) {
        uni.navigateTo({
          url:
            "/pages/mine/address/add?id=" +
            id +
            "&way=" +
            this.routerVal.way +
            "&type=order",
        });
      } else {
        uni.navigateTo({
          url:
            "/pages/mine/address/add?way=" + this.routerVal.way + "&type=order",
        });
      }
    },
    //设为默认地址
    setDefault(item) {
      delete item.updateBy;
      delete item.updateTime;
      delete item.deleteFlag;

      item.isDefault ? "" : (item.isDefault = !item.isDefault);

      API_Address.editAddress(item).then((res) => {
        uni.showToast({
          title: "设置默认地址成功",
          icon: "none",
        });
        this.getAddressList();
      });
    },
    onSwipeDelete(item, index) {
      this.deleteContent = "确定删除该地址？";
      this.deleteShow = true;
      this.goodsVal = item; // 记录要删除的地址
    },
    deleteConfirm() {
      // 调用删除接口
      API_Address.deleteAddress(this.goodsVal.id).then(res => {
        if (res.data.success) {
          uni.showToast({ title: "删除成功!", icon: "none" });
          this.getAddressList();
        }
      });
      this.deleteShow = false;
    }
  },
};
</script>

<style lang="scss" scoped>
@import "./address.scss";
.swipe-action-wrap {
  background: transparent !important;
  margin-bottom: 24rpx;
}
/deep/ .u-swipe-action {
  // border-radius: 20rpx !important;
  overflow: visible !important;
  background: transparent !important;
}

/deep/ .u-swipe-view {
  // border-radius: 20rpx !important;
  overflow: visible !important;
  background: transparent !important;
}

/deep/ .u-swipe-content {
  border-radius: 20rpx !important;
  overflow: visible !important;
  background: transparent !important;
}

.address-item-wrapper {
  margin-bottom: 20rpx;
  border-radius: 20rpx;
  overflow: hidden;
  background: transparent;
}

/deep/ .u-swipe-action__btn {
  border-radius: 0 20rpx 20rpx 0 !important;
  height: 100% !important;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx !important;
  min-width: 120rpx;
  box-sizing: border-box;
  overflow: hidden;
  padding: 0 32rpx;
  background: #ff4e3e !important;
  position: relative;
  z-index: 1;
}

/deep/ .u-swipe-action__btn::after {
  content: '';
  position: absolute;
  right: 0;
  top: 0;
  width: 20rpx;
  height: 100%;
  background: #ff4e3e;
  border-radius: 0 20rpx 20rpx 0;
  z-index: 2;
  pointer-events: none;
}

.swipe-item {
  // border-radius: 20rpx;
  box-shadow: 0 4rpx 24rpx 0 rgba(0,0,0,0.04);
  overflow: hidden;
  background: #fff;
  display: flex;
  align-items: stretch;
  width: 100%;
  min-height: 80rpx;
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

.basic, .edit {
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.edit {
  align-items: center;
}
</style>
