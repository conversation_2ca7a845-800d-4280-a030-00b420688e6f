<template>
  <view class="rank-page" :style="{ backgroundColor: backgroundColor}">
    <!-- <u-navbar :background="background" back-icon-color="#fff" :border-bottom="false"></u-navbar> -->
    <!-- 顶部推荐栏 -->
    <view class="top-banner" :style="{ 
      height: topBannerHeight + 'rpx',
      backgroundImage: backgroundBg ? `url(${backgroundBg})` : 'url()',
      backgroundSize: '100% 100%',
      backgroundRepeat: 'no-repeat'
    }">
     
      <view class="back-btn" :style="{ marginTop: safeAreaInsets.top + 'px' }">
        <u-icon name="arrow-left" :color="cornerColor" @click="goBack"  size="36"></u-icon>
      </view>
      
      
    </view>

    <!-- u-tabs 分类Tab栏 -->
    <view class="custom-tabs-bg" v-if="tabs.length > 0" :style="{ backgroundColor: backgroundColor }">
      <u-tabs
        :list="tabs"
        :is-scroll="false"
        :current="current"
        @change="onTabChange"
        :bg-color="backgroundColor"
        active-color="#fff"
        inactive-color="#b8a08a"
       
      ></u-tabs>
    </view>

    <!-- 排行榜列表（可滚动） -->
    <scroll-view
      class="rank-list-scroll"
      scroll-y="true"
      :style="{ 
        height: scrollHeight + 'px',
        backgroundColor: backgroundColor,
        marginTop: tabs.length === 0 ? '0' : '0'
      }"
    >
      <view class="rank-list">
        <view v-for="(item, idx) in rankList" :key="item.id" class="rank-item" @tap="geToGoods(item)">
          <view :class="['rank-badge', `rank-badge-${idx + 1}`]">
            <template v-if="idx === 0">
              <image src="/static/img/rank_1.png" class="badge-img" />
            </template>
            <template v-else-if="idx === 1">
              <image src="/static/img/rank_2.png" class="badge-img" />
            </template>
            <template v-else-if="idx === 2">
              <image src="/static/img/rank_3.png" class="badge-img" />
            </template>
            <!-- <template v-else>
              {{ idx + 1 }}
            </template> -->
          </view>
          <image
            :src="item.img"
            class="item-img"
            :lazy-load="true"
          />
          <view class="item-info">
            <view class="item-title">{{ item.title }}</view>
            <view class="item-box">
              <view class="item-price">￥{{ Number(item.price).toFixed(2) }}</view>
              <!-- <view class="item-sales">{{ item.sales }}+人购买</view> -->
            </view>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script>
import { getPageDataList } from "@/api/article";
export default {
  data() {
    return {
      tabs: [],
      current: 0,
      background: {
        backgroundColor: "#2d211b",
      },
      scrollHeight: 0,
      listWay: [],
      backgroundBg:'',
      backgroundColor: "#2d211b",
      topBannerHeight: 362, // 默认高度，单位rpx
      safeAreaInsets: 0,
      cornerColor: "#fff",
    };
  },
  onLoad(opacity) {
    // 初始计算scroll-view高度
    this.getPrivacyList(opacity.id);
    const { safeAreaInsets } = uni.getSystemInfoSync();
    this.safeAreaInsets = safeAreaInsets;
    
  },
  methods: {
    onTabChange(index) {
      this.current = index;
    },
    goBack(){
      uni.navigateBack();
    },
    // 计算scroll-view高度的方法
    calculateScrollHeight() {
      console.log("calculateScrollHeight",this.tabs.length);
      const topHeightPx = uni.upx2px(this.topBannerHeight + (this.tabs.length > 0 ? 90 : 0)); 
      const windowHeight = uni.getSystemInfoSync().windowHeight;
      this.scrollHeight = windowHeight - topHeightPx;
    },
    // 获取图片信息并设置高度
    getImageInfo(imageUrl) {
      return new Promise((resolve, reject) => {
        
        // 检查图片URL是否有效
        if (!imageUrl || imageUrl.trim() === '') {
          reject(new Error('图片URL为空'));
          return;
        }
        
        uni.getImageInfo({
          src: imageUrl,
          success: (res) => {
        
            resolve(res);
          },
          fail: (err) => {
           
            console.error('失败详情:', {
              errMsg: err.errMsg,
              errCode: err.errCode,
              imageUrl: imageUrl
            });
            reject(err);
          }
        });
      });
    },
    getImageSizeByElement(imageUrl) {
      return new Promise((resolve, reject) => {
      
        // #ifdef H5
        const img = new Image();
        img.onload = () => {
          resolve({ width: img.width, height: img.height });
        };
        img.onerror = (err) => {
       
          reject(new Error('图片加载失败'));
        };
        img.src = imageUrl;
        // #endif
        
        // #ifndef H5
        reject(new Error('非H5环境不支持此方法'));
        // #endif
      });
    },
    async getPrivacyList(val){
      const res = await getPageDataList(val);
      if (res.statusCode === 200) {
        const result = JSON.parse(res.data.result.pageData)
        console.log(result);
        this.backgroundBg = result.list[0].options.list[0].img;
        this.backgroundColor = result.list[1].options.list[0].titleWay[0].color;
        this.cornerColor = result.list[1].options.list[0].titleWay[0].cornerColor;
        console.log(this.cornerColor);
        
        // 获取图片信息并计算高度
        try {
          let imageInfo;
          try {
            imageInfo = await this.getImageInfo(this.backgroundBg);
          } catch (firstError) {
            imageInfo = await this.getImageSizeByElement(this.backgroundBg);
          }
          
          
         
          const screenWidth = uni.getSystemInfoSync().windowWidth;
          const imageRatio = imageInfo.height / imageInfo.width;
          const bannerHeightPx = screenWidth * imageRatio;
          
          // 将px转换为rpx：1rpx = 0.5px 
          const systemInfo = uni.getSystemInfoSync();
          const pxToRpxRatio = 750 / systemInfo.windowWidth; // 750rpx对应屏幕宽度
          this.topBannerHeight = bannerHeightPx * pxToRpxRatio;
          
          this.calculateScrollHeight();
          
          
        } catch (error) {
          this.topBannerHeight = 362; // 使用默认高度
          this.calculateScrollHeight();
        }
        
        
        console.log('this.tabs', result.list[1].options.list[0].titleWay);
        
        if (result.list[1].options.list[0].titleWay[0].title=='') {
          this.tabs = []
          this.calculateScrollHeight();
        }else{
          this.tabs = result.list[1].options.list[0].titleWay.map(item => {
            const { title, ...rest } = item;
            return { name: title, ...rest };
          });
          this.calculateScrollHeight();
        }
        this.listWay = result.list[1].options.list[0].listWay;
        
      }
    },
    geToGoods(item){
        uni.navigateTo({
          url:`/pages/product/goods?id=${item.id}&goodsId=${item.goodsId}`
        })
    }
  },
  computed: {
    rankList() {
      return (this.listWay || []).filter(item => item.___index === this.current);
    }
  }
};
</script>

<style scoped>
page {
    overflow: hidden;
}
.rank-page {
  background: #231815;
  min-height: 100vh;
  overflow: hidden;
}
.top-banner {
  width: 100%;
  padding: 40rpx 0 30rpx 0;
  text-align: center;
  color: #fff;
}
.back-btn {
  width: 100rpx;
}
.banner-title {
  font-size: 24rpx;
  margin-bottom: 10rpx;
  opacity: 0.8;
}
.banner-main {
  font-size: 44rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}
.banner-sub {
  font-size: 20rpx;
  opacity: 0.7;
}
.tab-bar {
  display: flex;
  background: #2d211b;
  border-radius: 20rpx;
  margin: 20rpx 20rpx 0 20rpx;
  overflow: hidden;
}
.tab-item {
  flex: 1;
  text-align: center;
  padding: 20rpx 0;
  color: #b8a08a;
  font-size: 28rpx;
  background: transparent;
}
.tab-item.active {
  color: #fff;
  font-weight: bold;
  border-bottom: 4rpx solid #e6c28b;
  background: #2d211b;
}
.rank-list {
  margin: 0 32rpx 0 32rpx;
  position: relative;
  padding-bottom: 10rpx;
}
.rank-item {
  display: flex;
  align-items: center;
  background: #fff;
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  padding: 20rpx;
  position: relative;
}
.rank-badge {
  /* width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  text-align: center;
  line-height: 48rpx;
  font-weight: bold;
  font-size: 28rpx;
  margin-right: 20rpx;
  position: absolute;
  top: 20rpx;
  left: 20rpx; */
  position: absolute;
  left: 20rpx;
  top: 0;
  z-index: 99;
}
/* .rank-badge-1 {
  background: #e6c28b;
  color: #fff;
}
.rank-badge-2 {
  background: #e0e0e0;
  color: #b8a08a;
}
.rank-badge-3 {
  background: #e6b1a1;
  color: #fff;
} */
.item-img {
  width: 160rpx;
  height: 160rpx;
  border-radius: 12rpx;
  margin-right: 20rpx;
  background: #f5f5f5;
  /* margin-left: 80rpx; */
}
.item-info {
  flex: 1;
  height: 80px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.item-title {
  font-size: 28rpx;
  color: #231815;
  margin-bottom: 10rpx;
  font-weight: 500;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
}
.item-box {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.item-price {
  color: #ff5134;
  font-size: 32rpx;
  margin-bottom: 6rpx;
}
.item-sales {
  color: #999999;
  font-size: 22rpx;
}
.custom-tabs-bg {
  width: 400rpx;
  padding: 0 20rpx;
  height: 90rpx;
}
.rank-list-scroll {
  /* 高度由js动态设置 */
  box-sizing: border-box;
}
.badge-img {
  width: 66rpx;
  height: 80rpx;
  
}
</style>
