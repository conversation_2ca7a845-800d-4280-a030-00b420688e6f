<template>
  <view class="guess-container">
    <!-- 标题 -->
    <view class="caption">
      <text class="text">{{ title }}</text>
    </view>
    <view class="guess">
      <view
        v-for="(item, index) in guessList"
        :key="`${item.goodsId}_${index}`"
        class="guess-item-wrapper"
      >
        <navigator
          class="guess-item"
          :url="`/pages/product/goods?id=${item.id}&goodsId=${item.goodsId}`"
        >
          <image class="image" mode="aspectFill" :src="item.img"></image>
          <view class="name"> {{ item.title }} </view>
          <view class="price">
            <text class="small">¥</text>
            <text> {{ $options.filters.goodsFormatPrice(item.price)[0] }}.{{ $options.filters.goodsFormatPrice(item.price)[1] }}</text>
          </view>
          <view class="sale_cart" @click.stop="addToCart(item)">
            <image
              src="/static/cart.png"
              mode="scaleToFill"
            />
          </view>
        </navigator>
        
      </view>
    </view>
    <view class="loading-text">
      {{ isEnd ? '没有更多数据了~' : '加载中...' }}
    </view>

    
  </view>
</template>
<script>
// import { getHomeLikeApi } from '@/services/home'
import { getGoodsList, getGoodsRelated } from '@/api/goods.js';
import * as API_trade from "@/api/trade.js"; // 引入购物车相关API

export default {
  name: 'Guess',
  props: {
    // 类型：home-首页猜你喜欢，goods-商品详情相关推荐,cart-购物车相关推荐
    type: {
      type: String,
      default: 'home'
    },
    // 商品ID，用于获取相关推荐
    goodsId: {
      type: [String, Number],
      default: ''
    },
    // 标题
    title: {
      type: String,
      default: '猜你喜欢'
    },
    // 新增：外部传递商品数据
    goodsList: {
      type: Array,
      default: () => []
    },
    show:{
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      // 分页参数
      pageParams: {
        page: 1,
        pageSize: 10,
      },
      guessList: [],
      // 已经结束标记
      isEnd: false,
      
      lightColor: '#ff5000',
    }
  },
  methods: {
    // 获取数据
    async getData() {
      // 如果有外部传递商品数据，直接用
      if (this.goodsList && Array.isArray(this.goodsList) && this.goodsList.length > 0) {
        this.guessList = [...this.goodsList];
        this.isEnd = true;
        return;
      }
      if (this.isEnd) {
        console.log('没有更多数据了');
        return uni.showToast({
          title: '没有更多数据了~',
          icon: 'none',
        })
      }
      return
      try {
        let res
        if (this.type === 'home') {
          // 获取猜你喜欢数据
          res = await getGoodsList(this.pageParams)
        } else if(this.type === 'goods') {
          // 获取相关推荐数据
          res = await getGoodsRelated(this.goodsId, this.pageParams)
        } else if(this.type === 'cart') {
          // 获取购物车相关推荐数据
          res = await getGoodsList(this.pageParams)
        }
        
        if (res.data && res.data.result && res.data.result.records) {
          this.guessList.push(...res.data.result.records)
          // 判断是否还有下一页
          if (this.pageParams.page < res.data.result.pages) {
            this.pageParams.page++
          } else {
            this.isEnd = true
          }
        }
      } catch (error) {
        console.error('加载数据失败', error);
        uni.showToast({
          title: '加载失败，请重试',
          icon: 'none'
        })
      }
    },
    // 重置数据
    resetData() {
      console.log('重置数据');
      this.guessList = []
      this.pageParams.page = 1
      this.isEnd = false
    },
    // 添加商品到购物车
    async addToCart(item) {
      console.log('添加商品到购物车',item);
      
      // 阻止事件冒泡，避免触发navigator的跳转
      try {
        uni.showLoading({
          title: '添加中...'
        });
        
        // 调用添加购物车接口
        const res = await API_trade.addToCart({
          skuId: item.id,
          num: 1
        });
        
        if (res.data.success) {
          uni.showToast({
            title: '添加成功',
            icon: 'success'
          });
          
          if (!this.show) {
            // 触发购物车数量更新
            uni.$emit('updateCartNum');
            // 发送添加购物车成功事件，通知cartList页面刷新
            uni.$emit('cartItemAdded', {
              skuId: item.id,
              goodsName: item.title,
              success: true
            });
          }
          
        } else {
          uni.showToast({
            title: res.data.message || '添加失败',
            icon: 'none'
          });
        }
      } catch (error) {
        console.error('添加购物车失败', error);
        uni.showToast({
          title: '添加失败，请重试',
          icon: 'none'
        });
      } finally {
        uni.hideLoading();
      }
    },
    
  },
  mounted() {
    this.getData();
  },
  watch: {
    // 监听类型变化，重新获取数据
    type() {
      this.resetData()
      this.getData()
    },
    // 监听商品ID变化，重新获取数据
    goodsId() {
      this.resetData()
      this.getData()
    },
    goodsList: {
      handler(newVal) {
        if (Array.isArray(newVal)) {
          this.guessList = [...newVal];
          this.isEnd = true;
        } else {
          // 确保 guessList 总是被初始化为数组
          this.guessList = [];
          this.isEnd = false;
        }
      },
      immediate: true // 这样初始也会触发
    }
  }
}
</script>



<style lang="scss">
:host {
  display: block;
}
/* 分类标题 */
.caption {
  display: flex;
  justify-content: center;
  line-height: 1;
  padding: 20rpx 0 ;
  font-size: 32rpx;
  color: #262626;

  .text {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0 28rpx 0 30rpx;

    &::before{
      content: '';
      width: 58rpx;
      height: 32rpx;
      background-image: url(@/static/bubble.png);
      background-size: contain;
      margin: 0 10rpx;
    }
    &::after {
      content: '';
      width: 58rpx;
      height: 32rpx;
      background-image: url(@/static/bubble_right.png);
      background-size: contain;
      margin: 0 10rpx;
    }
  }
}

/* 猜你喜欢 */
.guess {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  padding: 0 32rpx;

  .guess-item-wrapper {
    width: 48%;
    margin-bottom: 20rpx;
    position: relative;
  }

  .guess-item {
    width: 100%;
    padding: 24rpx 20rpx 20rpx;
    border-radius: 10rpx;
    overflow: hidden;
    background-color: #fff;
    box-sizing: border-box;
    position: relative;
    
  }

  .image {
    width: 290rpx;
    height: 290rpx;
  }
  .name {
    height: 70rpx;
    margin: 10rpx 0;
    font-size: 26rpx;
    color: #262626;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }
  .price {
    line-height: 1;
    padding-top: 4rpx;
    color: #FF6144;
    font-size: 40rpx;
    margin-top: 20rpx;
  }
  .small {
    font-size: 80%;
  }
  .sale_cart{
    position: absolute;
    right: 24rpx;
    bottom: 20rpx;
    image {
      width: 44rpx;
      height: 44rpx;
      display: block;
    }
  }
}
// 加载提示文字
.loading-text {
  text-align: center;
  font-size: 28rpx;
  color: #666;
  padding: 20rpx 0;
}
</style>
