<template>
  <view class="after-sales-policy-page">
    <u-parse :html="Article"></u-parse>
  </view>
</template>

<script>
import { getGoodsArticle } from "@/api/goods";
export default {
  data() {
    return {
      Article:'',
      policyMap: {
        1: { title: '七天无理由', type: 'SEVEN_SPEC' },
        2: { title: '售后政策', type: 'AFTER_SALE_SPEC' },
        3: { title: '退货运费规则', type: 'RETURN_FREIGHT_PRICE_SPEC' },
        4: { title: '退货违约金说明', type: 'RETURN_PENALTY_PRICE_SPEC' }
      }
    };
  },
  onLoad(opacity) {
    // 获取对应的配置
    const config = this.policyMap[opacity.id];
    if (config) {
      uni.setNavigationBarTitle({ title: config.title });
      this.getGoodsArticle(config.type);
    }
  },
  methods: {
    async getGoodsArticle(type) {
      let res =  await getGoodsArticle(type);
      if (res.data.success) {
        this.Article = res.data.result.content;
      }
     
    }
  },
};
</script>

<style lang="scss" scoped>
.after-sales-policy-page {
  line-height: 1.8;
  padding: 32rpx;
}

</style>
