<template>
  <view class="wrapper">
    <u-navbar class="my-title" title-size="32" :title="toUser.name"></u-navbar>
    <!-- 空盒子用来防止消息过少时 拉起键盘会遮盖消息 -->
    <view :animation="anData" style="height:0;">
    </view>
    <!-- 消息体 -->
    <!-- 用来获取消息体高度 -->
    <scroll-view id="msgList" 
      scroll-y="true" 
      :scroll-top="scrollTop"
      :scroll-with-animation="true"
      @scroll="handleScroll"
      @scrolltoupper="handleScrollToTop"
      class="msg-list-container"
      :scroll-into-view="scrollToView">
      <!-- 加载更多提示 -->
      <view class="loading-more" v-if="loading">
        <u-loading mode="circle" size="28"></u-loading>
        <text class="loading-text">加载中...</text>
      </view>
      <!-- 没有更多消息提示 -->
      <view class="no-more" v-if="!hasMore && !loading">
        <text class="no-more-text">没有更多消息了...</text>
      </view>
      <!-- 新消息提示 -->
      <view class="new-message-tip" v-if="hasNewMessage && !isAtBottom" @click="scrollToBottom(true)">
        <text class="new-message-text">新消息</text>
        <text class="new-message-count" v-if="newMessageCount > 1">{{newMessageCount}}条新消息</text>
      </view>
      <!-- 系统消息 -->
      <!-- 消息 -->
      <template v-if="msgList.length">
        <view class="flex-column-start" v-for="(item, index) in msgList" :key="item.id || 'local_' + index" :id="'msg-' + index">
          <view class="flex-row-start column-time">
            <view v-show="compareTime(index, item.createTime)" class="flex-row-start date-text">
              <text>{{beautifyTime(item.createTime)}}</text>
            </view>
          </view>
          <!-- 系统消息 -->
          <view v-if="item.messageType === 'SYSTEM'" class="system-message">
            <text>{{ item.text }}</text>
          </view>
          <!-- 用户消息 -->
          <view v-else-if="item.my" class="flex justify-end padding-right one-show align-start padding-top">
            <view class="flex justify-end" style="width: 400rpx;margin-top: 12px;">
              <view>
                <view class="user-name">{{ user.nickName }}</view>
                <view class="margin-left padding-chat bg-user-orang message-bubble" :class="{'message-new': item.isNew}">
                  <!-- 消息内容 -->
                  <text v-if="item.messageType === 'MESSAGE' && !emojistwo.includes(item.text)" style="word-break: break-all;">{{ item.text }}</text>
                  <text v-else-if="item.messageType === 'MESSAGE' && emojistwo.includes(item.text)" v-html="textReplaceEmoji(item.text)"></text>
                  <!-- 其他消息类型保持不变 -->
                  <view v-if="item.messageType == 'GOODS'">
                    <view class="goods-card u-flex u-row-between u-p-b-0" style="width:100%;margin: 0 0; ">
                      <view class="image-box" @click="jumpGoodDesc(item)">
                        <!-- <u-image shape="circle" :lazy-load="true" width="100" height="100"
                        :src="JSON.parse(item.text)['thumbnail']"></u-image> -->
                        <image class="image" :src="JSON.parse(item.text)['thumbnail']" mode="widthFix"></image>
                      </view>
                      <view class="goods-desc" @click="jumpGoodDesc(item)">
                        <view class="goods-desc-name">
                          <text class="goods-card-goods-name">{{ JSON.parse(item.text)['goodsName'] }}</text>
                        </view>
                        <view class="goods-desc-rice" >￥{{ JSON.parse(item.text)['price'] | unitPrice }}</view>
                      </view>
                    </view>
                  </view>
                  <view v-if="item.messageType == 'ORDER'" @click="linkTosOrders(item.text)">
                    <view class="order-sn">
                      <div class="wes">订单号：{{ JSON.parse(item.text).sn }}</div>
                      <template v-if="JSON.parse(item.text).orderItems && JSON.parse(item.text).orderItems.length">
                        <div class='order-item flex' v-for='(order,orderIndex) in JSON.parse(item.text).orderItems' :key="orderIndex">
                          <image  mode="widthFix" width='120rpx' height='120rpx' class="imageOrder"  :src="order.image" />
                          <!-- <image class="image" :src="order.image" mode="widthFix"></image> -->
                          <view class="name-or-time">
                            <div class="wes-1" >{{ order.goodsName }}</div>
                            <div class="main-color goods-desc-rice">{{ order.goodsPrice | unitPrice("￥") }}</div>
                          </view>
                        </div>
                      </template>
                      <!-- <view class="order-list">
                        <view class="order-time">
                            <text>{{ JSON.parse(item.text).paymentTime }}</text>
                          </view>
                      </view> -->
                    </view>
                  </view>
                </view>
              </view>
            </view>
            <view>
              <u-image shape="circle" :lazy-load="true" width="100" height="100"
              :src="user.face || '/static/missing-face.png'"></u-image>
              <!-- <u-avatar :src="user.face" :text="user.face ? '' : user.name" bg-color="#DDDDDD" img-mode="widthFix" size="100"></u-avatar> -->
            </view>
          </view>
          <!-- 接收人消息 -->
          <view v-else class="flex flex-row margin-left margin-top one-show">
            <view class="chat-img flex-row-center">
              <u-image shape="circle" :lazy-load="true" width="100" height="100"
              :src="toUser.face || '/static/missing-face.png'"></u-image>
              <!-- <u-avatar :src="toUser.face" :text="toUser.face ? '' : toUser.name" bg-color="#DDDDDD"></u-avatar> -->
            </view>
            <view class="flex" style="width: 500rpx;margin-top: 24rpx;">
              <view>
                <view class="other-name">{{ toUser.name }}</view>
                <view class=" padding-chat flex-column-start bg-to-color message-bubble" :class="{'message-new': item.isNew}">
                  <!-- 消息内容 -->
                  <text v-if="item.messageType === 'MESSAGE' && !emojistwo.includes(item.text)" style="word-break: break-all;">{{ item.text }}</text>
                  <text v-else-if="item.messageType === 'MESSAGE' && emojistwo.includes(item.text)" v-html="textReplaceEmoji(item.text)"></text>
                  <!-- 其他消息类型保持不变 -->
                  <view v-if="item.messageType == 'GOODS'">
                    <view class="goods-card u-flex u-row-between u-p-b-0" style="width:100%;margin: 0 0; ">
                      <view class="image-box" @click="jumpGoodDesc(item)">
                        <image class="image" :src="JSON.parse(item.text)['thumbnail']" mode="widthFix"></image>
                      </view>
                      <view class="goods-desc" @click="jumpGoodDesc(item)">
                        <view class="goods-desc-name">
                          <text class="goods-card-goods-name">{{ JSON.parse(item.text)['goodsName'] }}</text>
                        </view>
                          <view class="goods-desc-rice" >¥{{ JSON.parse(item.text)['price'] }}</view>
                      </view>
                    </view>
                  </view>
                  <view v-if="item.messageType == 'ORDER'" @click="linkTosOrders(item.text)">
                    <view class="order-sn">
                      <div class="wes">订单号：{{ JSON.parse(item.text).sn }}</div>
                      <template v-if="JSON.parse(item.text).orderItems && JSON.parse(item.text).orderItems.length">
                        <div class='order-item flex' v-for='(order,orderIndex) in JSON.parse(item.text).orderItems' :key="orderIndex">
                          <image  mode="widthFix" width='120rpx' height='120rpx' class="imageOrder"  :src="order.image" />
                          <!-- <image class="image" :src="order.image" mode="widthFix"></image> -->
                          <view class="name-or-time">
                            <div class="wes-1" >{{ order.goodsName }}</div>
                            <div class="main-color goods-desc-rice">{{ order.goodsPrice | unitPrice("￥") }}</div>
                          </view>
                        </div>
                      </template>
                      <view class="order-list">
                        <view class="order-time">
                            <text>{{ JSON.parse(item.text).paymentTime }}</text>
                          </view>
                      </view>
                    </view>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>
      </template>
      <!-- 防止消息底部被遮 -->
      <view id="message-end" style="height: 120rpx;">
      </view>
    </scroll-view>
    <!-- 底部导航栏 -->
    <view :style="{position: 'fixed', bottom:inputHeight+'px', height: (showOrderModel ? '300rpx' : ((showHideModel || localImGoodsId) ? '240rpx' : '110rpx'))}" class="flex-column-center bottom-dh"  :animation="animationData">
      <!-- 商品发送按钮 -->
      <view v-if="showHideModel || localImGoodsId" class="send-goods-btn" @click="showGoodsModel">
        <view class="close-btn" @click.stop="closeGoods">
          <text class="cuIcon-close"></text>
        </view>
        <view class="send-goods-content flex">
          <image class="goods-thumb" :src="goodListData.thumbnail" mode="aspectFill"></image>
          <view class="goods-info">
            <view class="goods-name">{{goodListData.goodsName}}</view>
            <view class="goods-price">¥{{goodListData.price}}</view>
          </view>
          <view class="send-btn">
            <text>发送</text>
          </view>
        </view>
      </view>
      
      <!-- 订单发送按钮 -->
      <view v-if="showOrderModel" class="send-order-btn" @click="sendOrderMessage">
        <view class="close-btn" @click.stop="closeOrder">
          <text class="cuIcon-close"></text>
        </view>
        <view class="send-order-content">
          
          <view class="order-info">
            <view class="order-sn1">订单号：{{orderData.order ? orderData.order.sn : ''}}</view>
            <view class="flex flex-row-start order-Send">
              <image v-if="orderData.orderItems && orderData.orderItems.length" class="imageSend" :src="orderData.orderItems[0].image" mode="widthFix" />
              <view>
                <view class="wes-1">{{orderData.orderItems && orderData.orderItems.length ? orderData.orderItems[0].goodsName : ''}}</view>
                <view class="order-amount">¥{{orderData.order ? orderData.order.goodsPrice : '0.00'}}</view>
              </view>
            </view>
            
          </view>
          <view class="send-btn">
            <text>发送订单</text>
          </view>
        </view>
      </view>
      
      <view class="bottom-dh-char flex-row-around" style="font-size: 55rpx;">
        <!-- vue无法使用软键盘"发送" -->

        <!-- #ifndef MP-WEIXIN -->
         <div v-if="!isShow" @click="navigateToBottom"  class="dh-input">
          用一句简短的话描述您的问题
         </div>
         <input v-show="isShow" :focus="isShow" @focus="inputBindFocus" @blur="eventHandle" :adjust-position="false" v-model="msg" class="dh-input" type="text" style="background-color: #f0f0f0;" @confirm="sendMessage"
         confirm-type="send"  placeholder="用一句简短的话描述您的问题" />
         <!-- #endif  -->

         <!-- #ifdef MP-WEIXIN -->
         <input  ref="inputRef"   @focus="inputBindFocus" @blur="eventHandle" :adjust-position="false" v-model="msg" class="dh-input" type="text" style="background-color: #f0f0f0;" @confirm="sendMessage"
         confirm-type="send"  placeholder="用一句简短的话描述您的问题" />
         <!-- #endif -->

        <view @click="sendMessage" class="cu-tag bg-main-color send round">
          发送 
        </view>
      </view>
    </view>
    <div id="bottom"></div>
  </view>
</template>

<script>
// rpx和px的比率
var l
// 可用窗口高度
var wh
// 顶部空盒子的高度
var mgUpHeight
import {
  getTalkMessage,
  getTalkByUser,
  jumpObtain
} from "@/api/im.js";
import { getOrderDetail } from "@/api/order.js";
import storage from "@/utils/storage.js";
import {
  beautifyTime
} from "@/utils/filters.js"
import config from '@/config/config.js'
import { textReplaceEmoji, emojistwo } from '@/utils/emojis.js';
export default {
  // 页面卸载后清除imGoodId
  onUnload () {
    // #ifdef H5
    uni.setStorageSync("imGoodId", '');
    // #endif

    // 只有在页面真正卸载时才关闭socket
    if (this.socketOpen) {
      this.socketOpen = false;
      uni.closeSocket();
    }
  },
  onLoad (options) {
    this.resolve = options
    
    // 优先处理订单信息 - 从订单详情进入时显示订单弹窗
    if (this.resolve.sn) {
      this.showOrderModel = true;
      this.getOrderDetails(this.resolve.sn);
    }
    // 如果没有订单号，但有商品ID，则显示商品弹窗
    else if (this.resolve.goodsid) {
      this.showHideModel = options.goodsid
      // 发送后刷新页面不显示 发送商品弹窗 local里面imGoodId不为空显示
      // #ifdef H5
      this.localImGoodsId = uni.getStorageSync("imGoodId");
      // #endif
      // 请求商品信息
      this.commodityDetails()
    }
 
    var query = uni.getSystemInfoSync()

    l = query.screenWidth / 750
    wh = query.windowHeight
    this.scrollHeight = (query.windowHeight - 44) + "px"
    this.user = storage.getUserInfo()
    this.toUser = storage.getTalkToUser()
    
    if (options.talkId) {
      this.params.talkId = options.talkId;
      this.getTalkMessage()
    } else {
      this.getTalk(options.userId)

    }
    // this.socket();
  },

  // 页面显示
  onShow() {
    // 如果连接断开，尝试重新连接
    if (!this.socketOpen) {
      this.socket();
    }
  },

  // 页面隐藏
  onHide() {
    
  },

  onPullDownRefresh () {
    // 当下拉刷新时，加载更多（更旧的）消息
    // loadMoreMessages 方法会处理页码递增和将消息添加到 msgList 的头部
    this.loadMoreMessages();
    setTimeout(function () {
      uni.stopPullDownRefresh();
    }, 1000);
  },

  onReady() {
    // #ifdef H5
    window.addEventListener('resize', this.handleWindowResize);
    // #endif

    // #ifdef APP-PLUS
    uni.onKeyboardHeightChange && uni.onKeyboardHeightChange(res => {
      console.log('键盘收起',res.height);
      if (res.height === 0) {
        console.log('键盘高度000',res.height);
        this.inputHeight = 0;
        this.$nextTick(() => {
          setTimeout(() => {
            // 只在不在底部时才滚动，避免页面跳动
            if (!this.isAtBottom) {
              this.scrollToBottom(true);
            }
          }, 300);
        });
      } else {
        this.inputHeight = res.height;
      }
    });
    // #endif
  },
  beforeDestroy() {
    // #ifdef H5
    window.removeEventListener('resize', this.handleWindowResize);
    // #endif
  },

  data () {
    return {
      textReplaceEmoji,
      emojistwo,
      socketOpen: false, //是否连接
      storage,
      fixed: 'fixed',
      bottom: '50px',
      width: '100%',
      showHideModel: undefined,
      localImGoodsId: '',
      showHide: true,
      msgLoad: false,
      anData: {},
      animationData: {},
      msgList: [],
      oldHeight: 0,
      params: { //搜索条件
        talkId: '',
        pageSize: 10,
        pageNumber: 1,
      },
      goToIndex: 0, // 前往位置
      msg: "",
      go: 0,
      newMessageNum: 0,
      user: {},
      toUser: {},
      scrollHeight: 0,
      resolve: {},
      goodListData: {},
      orderData: {}, // 订单数据
      showOrderModel: false, // 是否显示发送订单弹窗
      count: 0, //判断socket断开连接请求次数
      inputHeight:0,
      isShow:false,
      loading: false,
      hasMore: true,
      scrollToView: '',
      firstVisibleIndex: 0,
      scrollTop: 0,
      oldScrollHeight: 0,
      messageHeights: [],
      isFirstLoad: true,
      isLoadingMore: false,
      lastScrollTop: 0,
      scrollThreshold: 100,
      scrollTimer: null,
      scrollOffset: 100, // 增加滚动偏移量
      isScrolling: false, // 添加滚动状态标记
      isAtBottom: true,
      hasNewMessage: false,
      newMessageCount: 0,
      isFirstEntry: true, // 标记是否首次进入聊天室
    }
  },
  onPageScroll (e) {
 
    // #ifdef APP-PLUS
    uni.hideKeyboard()
    this.isShow = false
    // #endif
  },
  methods: {
    generateLocalTempId() {
      return 'local_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    },
    navigateToBottom(){
      // #ifdef H5
      this.isShow = true
      this.$nextTick(() => {
        setTimeout(() => {
          this.scrollToBottom();
        }, 100);
      });
      // #endif

      // #ifdef APP-PLUS
      this.$nextTick(() => {
        setTimeout(() => {
          this.scrollToBottom();
          setTimeout(() => { 
            this.isShow = true
          }, 200);
        }, 100);
      });
      // #endif
    },

    eventHandle(){
      this.inputHeight = 0
      this.isShow = false
      // 关键：键盘收起后滚动到底部
      this.$nextTick(() => {
        setTimeout(() => {
          this.scrollToBottom(true);
        }, 100);
      });
    },

    inputBindFocus(e){
      if (e.detail.height) {
        // #ifdef APP-PLUS
        if (uni.getSystemInfoSync().platform == 'ios') {
          this.inputHeight = e.detail.height - 40
        } else {
          this.inputHeight = e.detail.height
        }
        // #endif
        
        // #ifndef APP-PLUS
        this.inputHeight = e.detail.height
        // #endif
      }
      
      // 确保键盘弹出后滚动到底部
      this.$nextTick(() => {
        setTimeout(() => {
          this.scrollToBottom();
        }, 100);
      });
    },
    sendMessage () {
      if (this.msg == "") {
        return 0;
      }
      if (this.socketOpen == false) {
        this.socket(true);
        return 0;
      }
      let msg = {
        operation_type: "MESSAGE",
        to: this.toUser.userId,
        from: this.user.id,
        message_type: "MESSAGE",
        context: this.msg,
        talk_id: this.params.talkId,
      }
      
      let data = JSON.stringify(msg);
      uni.sendSocketMessage({
        data: data,
      });
      this.msgList.push({
        "text": this.msg,
        "my": true,
        "messageType": 'MESSAGE',
        id: null 
      });
      this.msg = "";
      
      // 发送消息后强制滚动到底部
      this.$nextTick(() => {
        setTimeout(() => { 
          this.scrollToBottom(true);
        }, 50); 
      });
    },
    sendGoodsMessage () {
      let msg = {
        operation_type: "MESSAGE",
        to: this.toUser.userId,
        from: this.user.id,
        message_type: "GOODS",
        context: this.goodListData,
        talk_id: this.params.talkId,
      }
      let data = JSON.stringify(msg);
      uni.sendSocketMessage({
        data: data
      });
      this.msgList.push({
        "text": JSON.stringify(this.goodListData),
        "my": true,
        "messageType": 'GOODS',
        id: null 
      });
      
      // 立即清除商品信息
      this.showHideModel = false;
      this.localImGoodsId = '';
      // #ifdef H5
      uni.setStorageSync("imGoodId", '');
      // #endif
      
      // 发送消息后强制滚动到底部
      this.$nextTick(() => {
        setTimeout(() => { 
          this.scrollToBottom(true);
        }, 50); 
      });
    },
    // isRetryConn 是否是重连标识
    socket(isRetryConn) {
      var _this = this;
      
      // 如果已经在连接中，不要重复连接
      if (this.socketOpen) {
        return;
      }

      try {
        //WebSocket的地址
        var url = config.baseWsUrl + '/' + storage.getAccessToken();
        
        // 连接
        uni.connectSocket({
          url: url,
        });

        // 监听WebSocket连接已打开
        uni.onSocketOpen(function (res) {
          _this.socketOpen = true;

		  if(isRetryConn){
			  //添加系统消息
			  _this.msgList.push({
			    id: 'system_' + Date.now() + '_1', // 临时唯一ID
			    messageType: 'SYSTEM', // 系统消息类型
			    text: '小臻已为您断线重连',
			    createTime: Date.now(), // 当前时间减1秒，确保显示在欢迎消息之前
			    fromUser: 'system', // 系统消息
			    to: _this.user.id, // 消息接收者为用户
			    my: false, // 这条消息不是我（用户）发送的
			  });
		  }else{
			  //添加系统消息
			  _this.msgList.push({
			    id: 'system_' + Date.now() + '_1', // 临时唯一ID
			    messageType: 'SYSTEM', // 系统消息类型
			    text: '小臻正在为您服务',
			    createTime: Date.now(), // 当前时间减1秒，确保显示在欢迎消息之前
			    to: _this.user.id, // 消息接收者为用户
			    my: false, // 这条消息不是我（用户）发送的
			  });
		  }
          
          // 滚动到底部
          _this.$nextTick(() => {
            setTimeout(() => {
              _this.scrollToBottom(true);
            }, 100);
          });
        });

        // 监听连接失败
        uni.onSocketError(function (err) {
          console.log("socket error here!", err);
          if (this.count < 3) {
            if (err && err.code != 1000) {
              setTimeout(() => {
                uni.connectSocket({
                  url: url,
                });
              }, 2000)
            }
          } else {
            uni.closeSocket();
          }
          this.count++
        });

        // 监听收到信息
        uni.onSocketMessage(function (res) {
          res.data = JSON.parse(res.data)
          console.log(res.data.result);
          if (res.data.messageResultType == 'MESSAGE') {
            const receivedMessage = res.data.result;
            let foundMatch = false;

            if (receivedMessage.fromUser === _this.user.id) {
              for (let i = _this.msgList.length - 1; i >= 0; i--) {
                const existingMessage = _this.msgList[i];

                if (existingMessage.my && existingMessage.id === null && existingMessage.messageType === receivedMessage.messageType) {
                  let contentMatch = false;
                  if (existingMessage.messageType === 'MESSAGE' && existingMessage.text === receivedMessage.context) {
                    contentMatch = true;
                  } else if (existingMessage.messageType === 'GOODS' && existingMessage.text === JSON.stringify(receivedMessage.context)) {
                    contentMatch = true;
                  }

                  if (contentMatch) {
                    _this.$set(existingMessage, 'id', receivedMessage.id);
                    _this.$set(existingMessage, 'createTime', receivedMessage.createTime);
                    _this.$set(existingMessage, 'isNew', true); 
                    foundMatch = true;
                    break; 
                  }
                }
              }
            }

            if (!foundMatch) {
              receivedMessage.isNew = true;
              _this.msgList.push(receivedMessage);
            }
            
            if (!_this.isAtBottom) {
              _this.hasNewMessage = true;
              _this.newMessageCount++;
            } else {
              _this.$nextTick(() => {
                _this.scrollToBottom(true);
              });
            }
            
            // 3秒后移除新消息标记
            setTimeout(() => {
              const indexToRemoveNewFlag = _this.msgList.findIndex(msg => msg.id === receivedMessage.id);
              if (indexToRemoveNewFlag !== -1) {
                _this.$set(_this.msgList[indexToRemoveNewFlag], 'isNew', false);
              }
            }, 3000);
          }
          console.log("From im servser msg:", res.data)
        })
      } catch (e) {
        uni.closeSocket();
      }
      // 监听是否断线，断线进行重新连接
      uni.onSocketClose((res) => {
        console.log("socket onSocketClose here!", res);
        this.socketOpen = false;
      })
    },
    beautifyTime,
    //订单详情
    linkTosOrders (val) {
      let order = JSON.parse(val)
      uni.navigateTo({
        url: '/pages/order/orderDetail?sn=' + order.sn,
      });

    },
    // 跳转商品详情页
    jumpGoodDesc (item) {
      let info = JSON.parse(item.text)
      uni.navigateTo({
        url: `/pages/product/goods?id=${info.id}&goodsId=${info.goodsId}`,
      });
    },

    //取消发送
    cancelModel () {
      this.showHide = false
    },
    // 请求商品详情
    commodityDetails () {
      jumpObtain(this.resolve.skuid, this.resolve.goodsid).then((res) => {
        this.goodListData = res.data.result.data
      })
    },
    // 获取订单详情
    getOrderDetails(sn) {
      getOrderDetail(sn).then((res) => {
        if (res.data.success) {
          this.orderData = res.data.result;
          console.log('订单详情数据:', this.orderData);
          console.log('订单基本信息:', this.orderData.order);
          console.log('订单商品列表:', this.orderData.orderItems);
        }
      }).catch((err) => {
        console.error('获取订单详情失败:', err);
      });
    },
    
    // 发送订单消息
    sendOrderMessage() {
      if (!this.orderData || !this.orderData.order) {
        uni.showToast({
          title: '订单信息不完整',
          icon: 'none'
        });
        return;
      }
      
      // 构造符合模板期望的数据结构
      const orderMessageData = {
        sn: this.orderData.order.sn,
        orderItems: this.orderData.orderItems || [],
        paymentTime: this.orderData.order.createTime,
        groupImages: this.orderData.orderItems && this.orderData.orderItems.length > 0 ? this.orderData.orderItems[0].image : '',
        groupName: this.orderData.orderItems && this.orderData.orderItems.length > 0 ? this.orderData.orderItems[0].goodsName : '',
        // 添加其他可能需要的字段
        order: this.orderData.order,
        orderItems: this.orderData.orderItems
      };
      
      let msg = {
        operation_type: "MESSAGE",
        to: this.toUser.userId,
        from: this.user.id,
        message_type: "ORDER",
        context: orderMessageData,
        talk_id: this.params.talkId,
      }
      let data = JSON.stringify(msg);
      uni.sendSocketMessage({
        data: data
      });
      this.msgList.push({
        "text": JSON.stringify(orderMessageData),
        "my": true,
        "messageType": 'ORDER',
        id: null 
      });
      
      // 立即清除订单信息
      this.showOrderModel = false;
      this.orderData = {};
      
      // 发送消息后强制滚动到底部
      this.$nextTick(() => {
        setTimeout(() => { 
          this.scrollToBottom(true);
        }, 50); 
      });
    },
    
    // 关闭订单信息
    closeOrder() {
      this.showOrderModel = false;
      this.orderData = {};
    },
    // 切换输入法时移动输入框(按照官方的上推页面的原理应该会自动适应不同的键盘高度-->官方bug)
    goPag (kh) {
      this.retractBox(0, 250)
      if (this.keyHeight != 0) {
        if (kh - this.keyHeight > 0) {
          this.retractBox(this.keyHeight - kh, 250)
        }
      }
    },
    // 移动顶部的空盒子
    messageBoxMove (x, t) {
      var animation = uni.createAnimation({
        duration: t,
        timingFunction: 'linear',
      })
      this.animation = animation
      animation.height(x).step()
      this.anData = animation.export()
    },
    // 保持消息体可见
    msgGo (type) {
      if (type === 'down') {
        this.scrollToBottom();
      }
    },
    // 回答问题的业务逻辑
    answer (id) {
      // 这里应该传入问题的id,模拟就用index代替了

    },
    // 不建议输入框聚焦时操作此动画
    ckAdd () {
      if (!this.showTow) {
        this.retractBox(-180, 350)
      } else {
        this.retractBox(0, 200)
      }
      this.showTow = !this.showTow
    },
    hideKey () {
      uni.hideKeyboard()
    },
    // 拉起/收回附加栏
    retractBox (x, t) {
      var animation = uni.createAnimation({
        duration: t,
        timingFunction: 'ease',
      })
      this.animation = animation
      animation.translateY(x).step()
      this.animationData = animation.export()
    },
    // 处理滚动到顶部事件
    handleScrollToTop() {
      if (this.loading || !this.hasMore) return;
      
      // 记录当前滚动位置
      this.lastScrollTop = this.scrollTop;
      
      // 加载更多消息
      this.loadMoreMessages();
    },

    // 处理滚动事件
    handleScroll(e) {
      const currentScrollTop = e.detail.scrollTop;
      this.lastScrollTop = currentScrollTop;
      
      // 检查是否在底部
      const query = uni.createSelectorQuery();
      query.select('#msgList').boundingClientRect(data => {
        if (!data) return;
        const scrollHeight = data.height;
        const clientHeight = data.height;
        this.isAtBottom = scrollHeight - currentScrollTop - clientHeight < 10;
        
        // 如果在底部，清除新消息提示
        if (this.isAtBottom) {
          this.hasNewMessage = false;
        }
      }).exec();
    },

    // 滚动到底部方法
    scrollToBottom(force = false) {
      if (!force && this.isAtBottom) {
        return;
      }
      this.scrollToView = 'message-end';
      this.$nextTick(() => {
        this.scrollToView = '';
        this.isAtBottom = true;
        this.hasNewMessage = false;
        this.newMessageCount = 0;
      });
    },

    // 加载更多消息方法
    async loadMoreMessages() {
      if (this.loading || !this.hasMore) return;
      
      this.loading = true;
      
      try {
        // 记录当前滚动区域高度和位置
        const query = uni.createSelectorQuery();
        query.select('#msgList').boundingClientRect(data => {
          if (!data) return;
          this.oldScrollHeight = data.height;
          this.lastScrollTop = this.scrollTop;
        }).exec();
        
        // 获取更多消息
        this.params.pageNumber += 1;
        const res = await getTalkMessage(this.params);
        
        if (res.data.success) {
          const newMessages = res.data.result;
          if (newMessages && newMessages.length > 0) {
            // 找到欢迎消息（如果存在）
            const welcomeMessageIndex = this.msgList.findIndex(msg => msg.id && msg.id.startsWith('system_'));
            const welcomeMessage = welcomeMessageIndex !== -1 ? this.msgList[welcomeMessageIndex] : null;
            
            // 如果存在欢迎消息，先移除它
            if (welcomeMessage) {
              // this.msgList.splice(welcomeMessageIndex, 1);
            }
            
            // 将新消息添加到列表前面
            this.msgList.unshift(...newMessages);
            
            // 标记消息归属
            this.msgList.forEach(item => {
              if (item.fromUser === this.user.id) {
                item.my = true;
              }
            });
            
            // 如果存在欢迎消息，确保它始终在最前面
            if (welcomeMessage) {
              // this.msgList.unshift(welcomeMessage);
            }
            
            // 等待DOM更新后调整滚动位置
            this.$nextTick(() => {
              query.select('#msgList').boundingClientRect(data => {
                if (!data) return;
                // 计算新增内容的高度差
                const heightDiff = data.height - this.oldScrollHeight;
                // 设置新的滚动位置，保持原有内容的位置不变
                this.scrollTop = heightDiff + this.lastScrollTop;
              }).exec();
            });
          } else {
            this.hasMore = false;
          }
        }
      } catch (error) {
        console.error('加载更多消息失败:', error);
      } finally {
        // 延迟关闭loading状态，让过渡效果更平滑
        setTimeout(() => {
          this.loading = false;
        }, 300);
      }
    },

    // 获取消息列表 (用于首次加载)
    async fetchInitialMessages() {
      try {
        const res = await getTalkMessage(this.params);
        if (res.data.success) {
          this.msgList = res.data.result; // 首次加载时直接替换列表
          this.msgList.forEach(item => {
            if (item.fromUser === this.user.id) {
              item.my = true;
            }
          });
          
          // 只有首次进入聊天室时才添加模拟客服欢迎消息
          // if (this.isFirstEntry) {
          //   // 添加欢迎消息
          //   this.msgList.unshift({
          //     id: 'system_' + Date.now(), // 临时唯一ID
          //     messageType: 'MESSAGE', // 作为普通消息类型处理
          //     text: '你好，我是臻小选客服小臻，很高兴为您服务，抱歉让您久等了，有什么可以帮忙，您也可以拨打4000015888客服热线，联络我司电话客服',
          //     createTime: Date.now(), // 当前时间
          //     fromUser: this.toUser.userId, // 消息发送者为客服
          //     to: this.user.id, // 消息接收者为用户
          //     my: false, // 这条消息不是我（用户）发送的
          //   });

          //   // 添加系统消息
          //   this.msgList.unshift({
          //     id: 'system_' + Date.now() + '_1', // 临时唯一ID
          //     messageType: 'SYSTEM', // 系统消息类型
          //     text: '小臻为您服务',
          //     createTime: Date.now() - 1000, // 当前时间减1秒，确保显示在欢迎消息之前
          //     fromUser: 'system', // 系统消息
          //     to: this.user.id, // 消息接收者为用户
          //     my: false, // 这条消息不是我（用户）发送的
          //   });
            
          //   this.isFirstEntry = false; // 标记为非首次进入
          // }
          
          // 确保在消息加载完成后滚动到底部
          this.$nextTick(() => {
            setTimeout(() => {
              this.scrollToBottom(true);
            }, 100);
          });
        }
      } catch (error) {
        console.error('获取消息失败:', error);
      }
    },

    // 获取聊天 (用于首次进入聊天页)
    async getTalk(userId) {
      try {
        const res = await getTalkByUser(userId);
        if (res.data.success) {
          this.toUser = res.data.result;
          this.params.talkId = res.data.result.id;
          // 首次进入时调用 fetchInitialMessages
          await this.fetchInitialMessages();
        }
      } catch (error) {
        console.error('获取聊天失败:', error);
      }
    },

    // 处理消息时间是否显示
    compareTime (index, datetime) {
      if (datetime == undefined) {
        return false;
      }
      if (typeof datetime == "number") {
        datetime = this.unixToDate(datetime, "yyyy-MM-dd hh:mm");
      }

      if (this.msgList[index].is_revoke == 1) {
        return false;
      }
      if (datetime) {
        datetime = datetime.replace(/-/g, "/");
      }

      let time = Math.floor(Date.parse(datetime) / 1000);
      let currTime = Math.floor(new Date().getTime() / 1000);

      // 当前时间5分钟内时间不显示
      if (currTime - time < 300) return false;
      // 判断是否是最后一条消息,最后一条消息默认显示时间
      if (index == this.msgList.length - 1) {
        return true;
      }
      
      // 检查下一条消息是否存在且有createTime
      const nextMessage = this.msgList[index + 1];
      if (!nextMessage || !nextMessage.createTime) {
        return true;
      }

      let nextDate;
      // 确保createTime是字符串类型
      if (typeof nextMessage.createTime === 'string') {
        nextDate = nextMessage.createTime.replace(/-/g, "/");
      } else if (typeof nextMessage.createTime === 'number') {
        nextDate = this.unixToDate(nextMessage.createTime, "yyyy-MM-dd hh:mm").replace(/-/g, "/");
      } else {
        return true;
      }

      // 比较时间差
      const timeDiff = Math.floor(Date.parse(nextDate) / 1000) - time;
      if (timeDiff < 300) return false;

      return !(
        this.unixToDate(new Date(datetime), "{y}-{m}-{d} {h}:{i}") ==
        this.unixToDate(new Date(nextDate), "{y}-{m}-{d} {h}:{i}")
      );
    },

    /**
     * 将unix时间戳转换为指定格式
     * @param unix   时间戳【秒】
     * @param format 转换格式
     * @returns {*|string}
     */
    unixToDate (unix, format) {
      if (!unix) return unix;
      let _format = format || "yyyy-MM-dd hh:mm:ss";
      const d = new Date(unix);
      const o = {
        "M+": d.getMonth() + 1,
        "d+": d.getDate(),
        "h+": d.getHours(),
        "m+": d.getMinutes(),
        "s+": d.getSeconds(),
        "q+": Math.floor((d.getMonth() + 3) / 3),
        S: d.getMilliseconds(),
      };
      if (/(y+)/.test(_format))
        _format = _format.replace(
          RegExp.$1,
          (d.getFullYear() + "").substr(4 - RegExp.$1.length)
        );
      for (const k in o)
        if (new RegExp("(" + k + ")").test(_format))
          _format = _format.replace(
            RegExp.$1,
            RegExp.$1.length === 1 ?
              o[k] :
              ("00" + o[k]).substr(("" + o[k]).length)
          );
      return _format;
    },

    // 显示商品发送弹窗
    showGoodsModel() {
      if (!this.goodListData || !this.goodListData.id) {
        uni.showToast({
          title: '商品信息不完整',
          icon: 'none'
        });
        return;
      }
      
      // 发送商品消息
      this.sendGoodsMessage();
      
      // 发送后清除商品信息
      this.showHideModel = false;
      // #ifdef H5
      uni.setStorageSync("imGoodId", '');
      // #endif
    },

    // 关闭商品信息
    closeGoods() {
      this.showHideModel = false;
      this.localImGoodsId = '';
      // #ifdef H5
      uni.setStorageSync("imGoodId", '');
      // #endif
    },
    handleWindowResize() {
      // H5下，window.innerHeight变化可判断键盘收起
      if (document.activeElement.tagName !== 'INPUT' && document.activeElement.tagName !== 'TEXTAREA') {
        this.$nextTick(() => {
          setTimeout(() => {
            this.scrollToBottom(true);
          }, 100);
        });
      }
    },
  }
}
</script>

<style lang="scss" scoped>
// 添加文本省略混入
@mixin text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.send{
  font-size: 24rpx !important;
}
.order-time {
  margin-top: 15rpx;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}

.wrapper {
  height: auto !important;
}

.order-list {
  display: flex;
  color: black;
  font-size: 20rpx;
  font-weight: bold;
  width: 95%;
}

.order-sn {
  width: 464rpx;
  background-color: #fff;
  border-radius: 20rpx;
  padding: 12rpx;
 
  
}

.name-or-time {
  margin: 15rpx 15rpx;
  
}


.goods-card {
  border-radius: 20rpx;
  margin-top: 15rpx;
  background-color: #ffffff;
  padding: 12rpx;
  width: 95%;
  // height: 120rpx;
  display: flex;
  flex-wrap: wrap;
  color: #302c2b;



  .image-box {
    width: 122rpx;
    height: 122rpx;
    overflow: hidden;

    .image {
      width: 122rpx;
      border-radius: 10rpx;
    }
  }

  .goods-desc {
    flex: 1;
    overflow: hidden;
    margin-left: 12rpx;
    width: 400rpx;

    .goods-desc-name {
      font-size: 30rpx;
      line-height: 1.5;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
      margin-bottom: 20rpx;


      .goods-card-goods-name {
       
        color: black;
        text-overflow: ellipsis;
        font-size: 26rpx;
        font-weight: bold;
      }
    }

    .price {
      margin-top: 50rpx;
      line-height: 2;
      margin-left: 5px;
      font-size: 26rpx;
      font-weight: 700;
    }
  }

  .send-goods {
    color: #ffffff;
    height: 50rpx;
    width: 130rpx;
    background-color: #f21c0c;
    font-size: 24rpx;
    border-radius: 17rpx;
    text-align: center;
    line-height: 50rpx;
    padding: 0 10rpx;
    position: relative;
    top: 20rpx;
    right: 20rpx;
  }
}

.cancel {
  color: #737373;
  position: relative;
  bottom: 40rpx;
  left: 12%;
}

.cart-message {
  display: flex;
  justify-content: center;
  align-items: center;
}

.bottom-dh-char {
  background-color: #f9f9f9;
  width: 750rpx;
  height: 110rpx;
  border-top: 1rpx solid #f5f5f5;
}

.user-name {
  text-align: right;
  font-size: 24rpx;
  color: #737373;
  margin-bottom: 10rpx;
  margin-right: 10rpx;
}

.other-name {
  text-align: left;
  font-size: 24rpx;
  color: #737373;
  margin-bottom: 10rpx;
  margin-left: 10rpx;
}
.dh-input {
  line-height: 65rpx;
  width: 500rpx;
  height: 65rpx;
  border-radius: 30rpx;
  padding-left: 15rpx;
  font-size: 22rpx;
  background-color: #FFFFFF;
}
.column-time {
  justify-content: center;
}



.chat-img {
  border-radius: 50%;
  width: 100rpx;
  height: 100rpx;
  background-color: #f7f7f7;
}

.padding-chat {
  padding: 17rpx 20rpx;
  border-radius: 20rpx;
}

.bg-user-orang {
  background-color: #FFE4C4;
  border-radius: 20rpx 4rpx 20rpx 20rpx;
}

.bg-to-color {
  background-color: #F5F5F5;
  border-radius: 4rpx 20rpx 20rpx 20rpx;
}

.message-bubble {
  transition: opacity 0.3s ease;
  max-width: 100%;
  word-break: break-all;
  
  &.message-new {
    animation: fadeIn 0.3s ease;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.tb-nv {
  width: 50rpx;
  height: 50rpx;
}

.goods-desc-rice{
  font-size: 24rpx;
  color: $main-color;
  font-weight: bold;
  margin-top: 10rpx;
}

.order-item{
  margin: 10rpx 0
}

uni-page-head {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 9999;
}

.bottom-dh {
  position: fixed;
  flex-shrink: 0;
  width: 100%;
  left: 0;
  bottom: 0;
  z-index: 199;
  padding-bottom: env(safe-area-inset-bottom);
  background: #FFFFFF;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  transition: height 0.3s ease;
}

.date-text {
  font-size: 24rpx;
  color: #999;
  margin: 20rpx 0;
  text-align: center;
}

.column-time {
  width: 100%;
  display: flex;
  justify-content: center;
  margin: 10rpx 0;
}

.loading-more {
  position: sticky;
  top: 0;
  z-index: 100;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx 0;
  background-color: #f8f8f8;
  width: 100%;
  
  .loading-text {
    font-size: 24rpx;
    color: #666;
    margin-left: 10rpx;
  }
}

.no-more {
  position: sticky;
  top: 0;
  z-index: 100;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx 0;
  background-color: #f8f8f8;
  width: 100%;
  
  .no-more-text {
    font-size: 24rpx;
    color: #666;
  }
}

.msg-list-container {
  height: calc(100vh - 200rpx);
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  position: relative;
}

.new-message-tip {
  position: fixed;
  bottom: 180rpx;
  left: 50%;
  transform: translateX(-50%);
  background-color: rgba(0, 0, 0, 0.6);
  padding: 10rpx 30rpx;
  border-radius: 30rpx;
  z-index: 100;
  display: flex;
  flex-direction: column;
  align-items: center;
  
  .new-message-text {
    color: #fff;
    font-size: 24rpx;
  }
  
  .new-message-count {
    color: #fff;
    font-size: 20rpx;
    margin-top: 4rpx;
  }
}

.send-goods-btn {
  width: 100%;
  background: #fff;
  border-top: 1px solid #eee;
  // padding: 20rpx;
  position: relative;
}

.send-order-btn {
  width: 100%;
  background: #fff;
  border-top: 1px solid #eee;
  padding: 20rpx;
  position: relative;
}

.close-btn {
  position: absolute;
  right: 20rpx;
  top: 8rpx;
  width: 40rpx;
  height: 40rpx;
  background: #f5f5f5;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.send-goods-content, .send-order-conten {
  width: 100%;
    background: #fff;
    padding: 16rpx 20rpx;
    border-bottom: 1rpx solid #f5f5f5;
    min-height: 112rpx;
    box-sizing: border-box;
    transition: all .3s ease;
    position: relative;
}



.goods-thumb, .imageSend {
  flex-shrink: 0;
  width: 80rpx;
  height: 80rpx;
  object-fit: contain;
  display: block;
  
}

.goods-info {
  flex: 1;
}

.order-info {
  flex: 1;
  .order-Send {
    margin-top: 10rpx;
    image {
      width: 80rpx;
      height: 80rpx;
      border-radius: 8rpx;
      margin-right: 20rpx;
    }
  }
}

.goods-name {
  width: 580rpx;
  font-size: 28rpx;
  color: #333;
  // margin-bottom: 10rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.order-sn {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.goods-price {
  font-size: 32rpx;
  color: #ff4444;
  font-weight: bold;
}

.order-amount {
  font-size: 32rpx;
  color: #ff4444;
  font-weight: bold;
}

.send-btn {
    position: absolute;
    right: 20rpx;
    bottom:-10rpx;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
    height: 40rpx;
    background: #ff3c2a;
    color: #fff;
    border-radius: 8rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 26rpx;
    padding: 20rpx;
}

.system-message {
  width: 100%;
  text-align: center;
  padding: 10rpx 0;
  
  text {
    display: inline-block;
    background-color: rgba(211, 212, 217, 0.2);
    color: #666;
    font-size: 24rpx;
    padding: 8rpx 24rpx;
    border-radius: 30rpx;
  }
}

.imageOrder {
  border-radius: 8rpx;
  width: 120rpx !important;
  height: 120rpx !important;
  object-fit: contain;
}

.wes-1 {
  width: 580rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.wes-2 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
</style>

<style lang="scss" scoped>
@import "./index-app.scss";
</style>
