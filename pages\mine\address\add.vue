<template>
  <view class="add-address">
    <div class="uForm">
      <u-form :border-bottom="false" :model="form" ref="uForm" :error-type="['toast']" :rule="rules">
				<!-- #ifndef H5 -->
        <!-- <view class="selectAddress" @click="clickUniMap">
          选择收货地址
        </view> -->
				<!-- #endif -->
        <u-form-item class="border" label="收货人" label-width="130" prop="name">
          <u-input v-model="form.name" clearable placeholder="请填写收货人姓名" :placeholder-style="placeholderStyle" />
        </u-form-item>

        <u-form-item label="手机号码" label-width="130" prop="mobile">
          <u-input v-model="form.mobile" type="number" maxlength="11" placeholder="请填写收货人手机号" :placeholder-style="placeholderStyle" />
        </u-form-item>
        <u-form-item label="所在区域" label-width="130" prop="___path">
          <view
            class="addressText"
            :class="{ 'addressText--selected': form.___path }"
            @click="showPicker"
          >
            {{ form.___path || '省市区县、乡镇等' }}
          </view>
        </u-form-item>
        <u-form-item class="detailAddress" label="详细地址" label-width="130" prop="detail">
          <u-input type="textarea" v-model="form.detail" maxlength="100" height="0" placeholder="请填写详细地址，具体到门牌号"  :placeholder-style="placeholderStyle"/>
        </u-form-item>
        <!-- <u-form-item label="地址别名" label-width="130">
          <u-input v-model="form.alias" placeholder="请输入地址别名" />
        </u-form-item> -->
        

   
      </u-form>

      <m-city :provinceData="list" headTitle="区域选择" ref="cityPicker" @funcValue="getpickerParentValue" pickerSize="4">
      </m-city>

      <uniMap v-if="mapFlag" @close="closeMap" @callback="callBackAddress" />
    </div>
    <view class="set-default-row">
          <text>设置默认地址</text>
          <u-checkbox :active-color="lightColor" v-model="form.isDefault" shape="circle" size="28"></u-checkbox>
    </view>
    <div class="saveBtn" @click="save">保存</div>
  </view>
</template>
<script>
import { addAddress, editAddress, getAddressDetail } from "@/api/address.js";
import city from "@/components/m-city/m-city.vue";
import uniMap from "@/components/uniMap";
import permision from "@/js_sdk/wa-permission/permission.js";
export default {
  components: {
    "m-city": city,
    uniMap,
  },
  onShow() {
    // 判断当前系统权限定位是否开启
  },
  methods: {
    // 关闭地图
    closeMap() {
      this.mapFlag = false;
    },
    // 打开地图并访问权限
    clickUniMap() {
      // #ifdef APP-PLUS
      if (plus.os.name == "iOS") {
        // ios系统
        permision.judgeIosPermission("location")
          ? (this.mapFlag = true)
          : this.refuseMap();
      } else {
        // 安卓
        this.requestAndroidPermission(
          "android.permission.ACCESS_FINE_LOCATION"
        );
      }
      // #endif

      // #ifndef APP-PLUS
      this.mapFlag = true;
      // #endif
    },

    // 如果拒绝权限 提示区设置
    refuseMap() {
      uni.showModal({
        title: "温馨提示",
        content: "您已拒绝定位,请开启",
        confirmText: "去设置",
        success(res) {
          if (res.confirm) {
            //打开授权设置
            // #ifndef MP-WEIXIN
            uni.getSystemInfo({
              success(res) {
                if (res.platform == "ios") {
                  //IOS
                  plus.runtime.openURL("app-settings://");
                } else if (res.platform == "android") {
                  //安卓
                  let main = plus.android.runtimeMainActivity();
                  let Intent = plus.android.importClass(
                    "android.content.Intent"
                  );
                  let mIntent = new Intent("android.settings.ACTION_SETTINGS");
                  main.startActivity(mIntent);
                }
              },
            });
            // #endif
          }
        },
      });
    },

    // 获取安卓是否拥有地址权限
    async requestAndroidPermission(permisionID) {
      var result = await permision.requestAndroidPermission(permisionID);

      if (result == 1) {
        this.mapFlag = true;
      } else {
        this.refuseMap();
      }
    },

    // 选择地址后数据的回调
    callBackAddress(val) {
      console.log(val)
      // uni.showLoading({
      //   title: "加载中",
      // });

      if (val.regeocode && val) {
        let address = val.regeocode;
        this.form.detail = address.formatted_address; //地址详情
        this.form.___path = val.data.result.name;
        this.form.consigneeAddressIdPath = val.data.result.id; // 地址id分割
        this.form.consigneeAddressPath = val.data.result.name; //地址名称， '，'分割
        this.form.lat = val.latitude; //纬度
        this.form.lon = val.longitude; //经度
         uni.hideLoading();
      }

      this.mapFlag = !this.mapFlag; //关闭地图
    },

    // 保存当前 地址
    save() {
      this.$refs.uForm.validate((valid) => {
        if (valid) {
          let pages = getCurrentPages(); //获取页面栈
          let beforePage = pages[pages.length - 2]; //上个页面

          // 如果没有id则为新增地址
          if (!this.form.id) {
            // 删除没有的数据
            delete this.form.___path;
            addAddress(this.form).then((res) => {
              if (res.data.success) {
                uni.navigateBack();
              }
            });
          } else {
            // 修改地址
            delete this.form.___path;
            delete this.form.updateBy;
            delete this.form.updateTime;
            editAddress(this.form).then((res) => {
              if (res.data.success) {
                uni.navigateBack();
              }
            });
          }
        }
      });
    },

    // 三级地址联动回调
    getpickerParentValue(e) {
      // 将需要绑定的地址设置为空，并赋值
      this.form.consigneeAddressIdPath = [];
      this.form.consigneeAddressPath = [];
      let name = "";

      e.forEach((item, index) => {
        if (item.id) {
          // 遍历数据
          this.form.consigneeAddressIdPath.push(item.id);
          this.form.consigneeAddressPath.push(item.localName);
          name += item.localName;
          this.form.___path = name;
        }
        if (index == e.length - 1) {
          //如果是最后一个
          let _town = item.children.filter((_child) => {
            return _child.id == item.id;
          });

          this.form.lat = _town[0].center.split(",")[1];
          this.form.lon = _town[0].center.split(",")[0];
        }
      });
    },

    // 显示三级地址联动
    showPicker() {
      this.$refs.cityPicker.show();
    },
  },
  mounted() {},
  data() {
    return {
      lightColor: this.$lightColor, //高亮颜色
      mapFlag: false, // 地图选择开
      routerVal: "",
      form: {
        detail: "", //地址详情
        name: "", //收货人姓名
        mobile: "", //手机号码
        consigneeAddressIdPath: [], //地址id
        consigneeAddressPath: [], //地址名字
        ___path: "", //所在区域
        isDefault: false, //是否默认地址
      },
      // 表单提交校验规则
      rules: {
        name: [
          {
            required: true,
            message: "收货人姓名不能为空",
            trigger: ["blur", "change"],
          },
        ],
        mobile: [
          {
            required: true,
            message: "手机号码不能为空",
            trigger: ["blur", "change"],
          },
          {
            validator: (rule, value, callback) => {
              return this.$u.test.mobile(value);
            },
            message: "手机号码不正确",
            trigger: ["change", "blur"],
          },
        ],
        ___path: [
          {
            required: true,
            message: "请选择所在区域",
            trigger: ["change"],
          },
        ],
        detail: [
          {
            required: true,
            message: "请填写详细地址",
            trigger: ["blur", "change"],
          },
        ],
      },
      list: [
        {
          id: "",
          localName: "请选择",
          children: [],
        },
      ],
      placeholderStyle: 'font-size: 32rpx;color: rgba(153,153,153,0.55);',
    };
  },
  onLoad(option) {
    // uni.showLoading({
    //   title: "加载中",
    // });
    this.routerVal = option;
    // 如果当前是编辑地址,则需要查询出地址详情信息
    if (option.id) {
      getAddressDetail(option.id).then((res) => {
        const params = res.data.result;
        params.___path = params.consigneeAddressPath;
        this.$set(this, "form", params);

         if (this.$store.state.isShowToast){ uni.hideLoading() };
      });
    }
     uni.hideLoading();
  },
  // 初始化rules必须要在onReady生命周期，因为onLoad生命周期组件可能尚未创建完毕
  onReady() {
    this.$refs.uForm.setRules(this.rules);
  },
};
</script>
<style scoped lang="scss">
.add-address {
  width: 100vw;
  min-height: 100vh;
  background: #f7f7f7;
  padding-top: 32rpx;
}

.uForm {
  width: 92vw;
  margin: 0 auto;
  background: #fff;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 24rpx 0 rgba(0,0,0,0.04);
  // padding: 0 0 40rpx 0;
  overflow: hidden;
}

.u-form-item {
  background: #fff;
  padding: 16rpx 32rpx;
  font-size: 30rpx;
  // border-bottom: 1px solid #f0f0f0;
  &:last-child {
    border-bottom: none;
  }
  /deep/ .u-input__input {
    font-size: 30rpx;
    height: 56rpx;
    line-height: 56rpx;
    color: #222;
    text-align: right;
  }
  /deep/ .u-input__input::placeholder {
    color: #999999;
    font-size: 28rpx;
    
  }
}

.detailAddress /deep/ .u-form-item--left {
  align-items: flex-start;
}

.saveBtn {
  width: 90vw;
  height: 88rpx;
  line-height: 88rpx;
  text-align: center;
  font-size: 32rpx;
  font-weight: 500;
  background: linear-gradient( 90deg, #FF5235 0%, #FF9500 100%);
  color: #fff;
  border-radius: 44rpx;
  margin: 60rpx auto 0 auto;
  box-shadow: 0 8rpx 24rpx 0 rgba(255,124,62,0.12);
  position: absolute;
  bottom: 40rpx;
  left: 50%;
  transform: translateX(-50%);
}

.set-default-row {
  width: 686rpx;
  height: 104rpx;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: space-between;
  // padding: 32rpx 36rpx;
  padding: 32rpx 0 32rpx 36rpx;
  font-size: 30rpx;
  color: #222;
  background: #fff;
  border-radius: 20rpx;
  margin-top: 20rpx;
  margin: 20rpx auto;
}

.selectAddress {
  margin-top: 40rpx;
  background: #fff;

  color: $aider-light-color;
  border: 2rpx solid $aider-light-color;
}

.u-btn {
  margin: 30rpx 30rpx 0 30rpx;
  background-color: $main-color;
}

/deep/.u-checkbox {
  // margin: 30rpx 30rpx 0 30rpx;

  .u-label-class.u-checkbox__label {
    color: $font-color-light;
    font-size: $font-sm;
  }
}

/deep/ .u-checkbox__label {
  font-size: 28rpx;
}

.input-row {
  display: flex;
  align-items: center;
  width: 100%;
}
.input-flex {
  flex: 1;
  min-width: 0;
  /deep/ .u-input__input {
    width: 100% !important;
  }
}
.input-tip {
  margin-left: 16rpx;
  color: #bbb;
  font-size: 28rpx;
  white-space: nowrap;
  flex-shrink: 0;
}
.addressText {
  width: 100%;
  font-weight: 400;
  font-size: 32rpx;
  color: rgba(153,153,153,0.55);
  text-align: right;
  transition: color 0.2s;
  // 一行显示超出...
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-box-orient: vertical;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  white-space: normal;

}
.addressText--selected {
  color: #222; // 选中后黑色
}

</style>
