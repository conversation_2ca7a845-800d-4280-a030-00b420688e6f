<template>
  <view class="container">
   
    <view class="msg-detail-card">
      <view class="msg-header">
        <view class="msg-title">
          {{ msg.title }}
          <text v-if="msg.status === 'UN_READY'" class="dot"></text>
        </view>
        <view class="msg-time">{{ msg.time || '星期一' }}</view>
      </view>
      <view class="msg-body">
        <!-- <image :src="msg.img || '/static/img/goods.png'" class="goods-img" mode="aspectFill" /> -->
        <view class="msg-sub">{{ msg.content }}</view>
        <!-- <view class="msg-desc">{{ msg.desc || '您购买的天瑞优品 丝苗米5KG' }}</view> -->
        <view class="msg-link" v-if="msg.refId" @click="goOrderDetail(msg)">查看详情 &gt;</view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      msg: {
        title: '',
        content: '',
        desc: '',
        img: '',
        status: '',
        time: ''
      }
    }
  },
  onLoad(options) {
    if (options.data) {
      const msgData = JSON.parse(decodeURIComponent(options.data));
      this.msg = {
        ...msgData,
        // 兼容字段
        time: msgData.createTime || '星期一',
        img: msgData.img || '/static/img/goods.png',
        desc: msgData.desc || msgData.content
      }
    }
  },
  methods: {
    goOrderDetail(val) {
        console.log(val);
      if (val.messageTags === "AFTER_SALE") {
        // 跳转到售后详情页
        uni.navigateTo({ url: '/pages/order/afterSales/applyDetail?sn=' + val.refId });
      } else {
        // 跳转到订单详情页
        uni.navigateTo({ url: '/pages/order/orderDetail?sn=' + val.refId });
      }
      
    }
  }
}
</script>

<style>
.container {
  background: #f7f7f7;
  min-height: 100vh;
  padding-bottom: 40rpx;
}
.msg-detail-card {
  background: #fff;
  border-radius: 20rpx;
  margin: 32rpx 24rpx 0 24rpx;
  padding: 32rpx 24rpx 32rpx 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.03);
}
.msg-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}
.msg-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #222;
  display: flex;
  align-items: center;
}
.dot {
  display: inline-block;
  width: 16rpx;
  height: 16rpx;
  background: #FF3B30;
  border-radius: 50%;
  margin-left: 12rpx;
}
.msg-time {
  font-size: 24rpx;
  color: #bbb;
}
.msg-body {
  margin-top: 24rpx;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}
.goods-img {
  width: 100rpx;
  height: 100rpx;
  border-radius: 12rpx;
  margin-bottom: 16rpx;
  background: #f2f2f2;
}
.msg-sub {
  font-size: 28rpx;
  color: #444;
  margin-bottom: 8rpx;
}
.msg-desc {
  font-size: 24rpx;
  color: #888;
  margin-bottom: 16rpx;
}
.msg-link {
  font-size: 26rpx;
  color: #007aff;
  margin-top: 8rpx;
}
</style>
