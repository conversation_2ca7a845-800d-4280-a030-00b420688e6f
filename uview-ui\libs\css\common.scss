.u-relative,
.u-rela {
	position: relative;
}

.u-absolute,
.u-abso {
	position: absolute;
}

// nvue不能用标签命名样式，不能放在微信组件中，否则微信开发工具会报警告，无法使用标签名当做选择器
/* #ifndef APP-NVUE */
image {
	display: inline-block;
}

// 在weex，也即nvue中，所有元素默认为border-box
view,
text {
	box-sizing: border-box;
}
/* #endif */

.u-font-xs {
	font-size: 22rpx;
}

.u-font-sm {
	font-size: 26rpx;
}

.u-font-md {
	font-size: 28rpx;
}

.u-font-lg {
	font-size: 30rpx;
}

.u-font-xl {
	font-size: 34rpx;
}

.u-flex {
	/* #ifndef APP-NVUE */
	display: flex;
	/* #endif */
	flex-direction: row;
	align-items: center;
}

.u-flex-wrap {
	flex-wrap: wrap;
}

.u-flex-nowrap {
	flex-wrap: nowrap;
}

.u-col-center {
	align-items: center;
}

.u-col-top {
	align-items: flex-start;
}

.u-col-bottom {
	align-items: flex-end;
}

.u-row-center {
	justify-content: center;
}

.u-row-left {
	justify-content: flex-start;
}

.u-row-right {
	justify-content: flex-end;
}

.u-row-between {
	justify-content: space-between;
}

.u-row-around {
	justify-content: space-around;
}

.u-text-left {
	text-align: left;
}

.u-text-center {
	text-align: center;
}

.u-text-right {
	text-align: right;
}

.u-flex-col {
	/* #ifndef APP-NVUE */
	display: flex;
	/* #endif */
	flex-direction: column;
}

// 定义flex等分
// @for $i from 0 through 12 {
// 	.u-flex-#{$i} {
// 		flex: $i;
// 	}
// }

// // 定义字体(px)单位，小于20都为px单位字体
// @for $i from 9 to 20 {
// 	.u-font-#{$i} {
// 		font-size: $i + px;
// 	}
// }

// // 定义字体(rpx)单位，大于或等于20的都为rpx单位字体
// @for $i from 20 through 40 {
// 	.u-font-#{$i} {
// 		font-size: $i + rpx;
// 	}
// }

// // 定义内外边距，历遍1-80
// @for $i from 0 through 80 {
// 	// 只要双数和能被5除尽的数
// 	@if $i % 2 == 0 or $i % 5 == 0 {
// 		// 得出：u-margin-30或者u-m-30
// 		.u-margin-#{$i}, .u-m-#{$i} {
// 			margin: $i + rpx!important;
// 		}
		
// 		// 得出：u-padding-30或者u-p-30
// 		.u-padding-#{$i}, .u-p-#{$i} {
// 			padding: $i + rpx!important;
// 		}
		
// 		@each $short, $long in l left, t top, r right, b bottom {
// 			// 缩写版，结果如： u-m-l-30
// 			// 定义外边距
// 			.u-m-#{$short}-#{$i} {
// 				margin-#{$long}: $i + rpx!important;
// 			}
			
// 			// 定义内边距
// 			.u-p-#{$short}-#{$i} {
// 				padding-#{$long}: $i + rpx!important;
// 			}
			
// 			// 完整版，结果如：u-margin-left-30
// 			// 定义外边距
// 			.u-margin-#{$long}-#{$i} {
// 				margin-#{$long}: $i + rpx!important;
// 			}
			
// 			// 定义内边距
// 			.u-padding-#{$long}-#{$i} {
// 				padding-#{$long}: $i + rpx!important;
// 			}
// 		}
// 	}
// }

