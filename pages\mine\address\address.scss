.empty {
    margin-top: 200rpx !important;
  }
  .alifont {
    display: inline-block;
  }
  
  .region {
    span {
      margin: 0 4rpx !important;
    }
  }
  .address {
    background: #f7f7f7;
    min-height: 100vh;
    .list {
      padding: 20rpx 32rpx 0 32rpx;
      position: relative;
      .item {
        // background: #fff;
        // border-radius: 20rpx;
        // box-shadow: 0 4rpx 24rpx 0 rgba(0,0,0,0.04);
        // margin-bottom: 24rpx;
        // display: flex;
        // align-items: stretch;
        // padding: 0;
        .basic {
          flex: 1;
          padding: 32rpx;
          display: flex;
          flex-direction: column;
          justify-content: center;
          .row {
            display: flex;
            align-items: center;
          }
          .default-label {
            width: 52rpx;
            height: 28rpx;
            background: linear-gradient( 90deg, #FF5235 0%, #FF9500 100%);
            border-radius: 4rpx;
            color: #fff;
            font-size: 16rpx;
            text-align: center;
            line-height: 28rpx;
            margin-right: 12rpx;
          }
          .name {
            font-weight: 400;
            font-size: 32rpx;
            color: #000000;
            margin-right: 40rpx;
          }
          .mobile {
            font-weight: 400;
            font-size: 32rpx;
            color: #000000;
          }
          .region-text {
            width: 420rpx;
            // height: 80rpx;
            font-size: 26rpx;
            color: #000;
            margin-top: 12rpx;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: normal;
          }
        }
        
        .edit {
          width: 176rpx;
          // border-left: 1px solid #f2f2f2;
          position: relative;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          &::before {
            content: "";
            position: absolute;
            left: 0;
            top: 32rpx; 
            bottom: 32rpx; 
            width: 1px;
            background: #ededed; 
            border-radius: 1px;
          }
          .edit-btn {
            color: #000000;
            font-size: 28rpx;
            font-weight: 400;
          }
        }
      }
    }
    .btn {
      position: fixed;
      left: 30rpx;
      right: 30rpx;
      bottom: 60rpx;
      height: 88rpx;
      border-radius: 44rpx;
      background: linear-gradient( 90deg, #FF5235 0%, #FF9500 100%);
      color: #fff;
      font-size: 32rpx;
      font-weight: 400;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 8rpx 24rpx 0 rgba(255,124,62,0.12);
      .u-icon {
        margin-right: 16rpx;
      }
    }
  }