<template>
  <view>
    <!-- 订单状态 -->
    <view class="info-view order-view">
      <view class="order-status" v-if="orderStatusList[order.orderStatus]">
        <view class="Countdown" v-if="order.orderStatus == 'UNPAID'">
          还剩
          <u-count-down
            :timestamp="timeFn(orderDetail.autoCancel)"
            font-size="24rpx"
            bg-color="#FFE3DA"
            color="#FF5134"
            separator-size="24rpx"
            separator-color="#FF5134"
          ></u-count-down>
          订单自动取消
        </view>
        <text v-else>{{ orderStatusList[order.orderStatus].title }}</text>

        <text v-if="order.orderStatus == 'UNPAID'"></text>
        <!-- <div>{{ orderStatusList[order.orderStatus].value }}</div> -->
        <!-- 物流状态 -->
        <view v-if="logisticsList.traces.length>0" class="logistics-card logistics-timeline">
          <view class="timeline-icon">
            <image
              src="/static/img/logistics.png"
              mode="scaleToFill"
              class="timeline-logo"
            />
            <!-- <image :src="logisticsInfo.logo || 'static/img/logistics.png'" class="timeline-logo" /> -->
            <view class="timeline-line"></view>
            <view class="timeline-dot"></view>
          </view>
          <view class="timeline-content">
            <view class="logistics-header">
              <view class="logistics-box">
                {{ logisticsInfo.state }}
                <view style="font-weight: bold; font-size: 32rpx;color: #333333;">{{ logisticsList.state==3?'已收货':'运输中'  }}</view>
                <view class="timeline_right" @click="gotoLogisticsDetail">
                  <text style="display: inline-block;">详情</text>
                  <u-icon name="arrow-right" style="color: #999; display: inline-block;"></u-icon>
                </view>
              </view>
              
              <view class="timeline_right_lastAcceptStation" style="font-size: 24rpx; color: #666">
                快件到达{{ lastAcceptStation }}
              </view>
            </view>
            <view class="logistics-address" style="margin-top: 40rpx">
              <view style="color: #333; font-size: 28rpx;" class="address_order">
                {{ order.consigneeAddressPath }}{{ order.consigneeDetail }}
              </view>
              <view style="color: #666; font-size: 24rpx; margin-top: 8rpx">
                {{ order.consigneeName }} {{ order.consigneeMobile | secrecyMobile }}
                <text class="protect_bg" v-if="logisticsInfo.protect"> 号码保护中</text>
              </view>
            </view>
          </view>
        </view>
        <!-- 地址 -->
        <block v-else>
          <view
          class="info-view address_box"
          v-if="
            order.deliveryMethod === 'LOGISTICS' &&
            order.orderType !== 'VIRTUAL'
          "
        >
          <view class="address-view">
            <view>
              <view class="address-title address_title">
                <span>{{ order.consigneeName || "未填写昵称" }}</span>
                <span>{{
                  order.consigneeMobile || "未填写手机号" | secrecyMobile
                }}</span>
              </view>
              <view class="address address_boxText">
                <image
                  src="/static/address.png"
                  mode="scaleToFill"
                  style="
                    width: 40rpx;
                    height: 40rpx;
                    margin-right: 20rpx;
                    display: block;
                  "
                />
                <view class="address_boxText_text_cent">
                  {{ order.consigneeAddressPath }}
                  {{ order.consigneeDetail }}</view
                >
                <!-- <u-icon name="arrow-right" style="color: #bababa"></u-icon> -->
              </view>
            </view>
          </view>
          </view>
        </block>
       
      </view>
    </view>
    <!-- 物流信息 -->
    <!-- <view class="info-view logistics-view">

      <view class="logistics-List">
        <view class="verificationCode" v-if="order.verificationCode">
          券码： {{ order.orderStatus == 'CANCELLED' ?  '已失效' : order.verificationCode }}
        </view>
		<view @click="handleClickDeliver()" class="info-view logi-view"  v-else-if="orderPackage && orderPackage.length">
		  <view class="verificationCode">
		      当前订单有 {{ orderPackage.length }} 个包裹快递
		  </view>
		  <div>
		      点击此处查看
		  </div>
		</view>
        <view v-else class="logistics-List-title">
          {{ '暂无物流信息' }}
        </view>
      </view>

    </view> -->
    <!-- 地址 -->
    <!-- <view class="info-view" v-if="order.deliveryMethod === 'LOGISTICS' && order.orderType !== 'VIRTUAL'">
      <view class="address-view">
        <view>
          <view class="address-title">
            <span>{{ order.consigneeName || "未填写昵称" }}</span>
            <span>{{ order.consigneeMobile || "未填写手机号" | secrecyMobile }}</span>
          </view>
          <view class="address">地址：{{ order.consigneeAddressPath }}
            {{ order.consigneeDetail }}</view>
        </view>
      </view>
    </view> -->

    <!-- 提货地址 -->
    <!-- <view class="info-view" v-if="order.deliveryMethod === 'SELF_PICK_UP'">
      <view class="address-view">
        <view>
          <view class="order-info-view">
            <view class="title">自提点地址:</view>
            <view class="value address-line-height">{{ order.storeAddressPath }}</view>
          </view>
          <view class="order-info-view" @click="callPhone" >
            <view class="title">联系方式:</view>
            <view class="value">{{ order.storeAddressMobile }}<u-icon name='phone-fill' ></u-icon></view>
          </view>
         
        </view>
      </view>
    </view> -->

    <!-- 商品信息 -->
    <!-- #ifdef H5 -->
    <view
      class="shop-info-view"
      :style="{ 'margin-top': logisticsList && logisticsList.traces && logisticsList.traces.length > 0 ? '170rpx' : '100rpx' }"
    >
    <!-- #endif -->
    <!-- #ifdef APP -->
    <view
      class="shop-info-view"
      :style="{ 'margin-top': logisticsList && logisticsList.traces && logisticsList.traces.length > 0 ? '160rpx' : '90rpx' }"
    >
    <!-- #endif -->
  
      <view class="seller-view">
        <!-- 店铺名称 -->
        <view class="seller-info u-flex u-row-between" style="padding: 0">
          <view class="seller-name" @click="goToShopPage(order)">
            <view class="name name_text">商品信息</view>
            <!-- <view class="status" v-if="orderStatusList[order.orderStatus]">
              {{ orderStatusList[order.orderStatus].title }}</view
            > -->
          </view>
          <view class="order-sn"></view>
        </view>
        <view style="margin-top: 32rpx">
          <view v-for="(sku, skuIndex) in orderGoodsList" :key="skuIndex">
            <view class="goods-item-view" >
              <view
                class="goods-img"
                @click="gotoGoodsDetail(sku)"
                style="margin-right: 20rpx"
              >
                <u-image
                  border-radius="6"
                  width="148rpx"
                  height="148rpx"
                  :src="sku.image"
                ></u-image>
              </view>
              <view
                class="goods-info goods_info"
                @click="gotoGoodsDetail(sku)"
                style="padding: 0"
              >
                <view class="goods-title u-line-2" style="margin: 0">{{
                  sku.goodsName
                }}</view>

                <view class="goods_specs">{{
                  formatSpecs(sku.specs) || sku.skuName || ""
                }}</view>
                <view class="goods-price">
                  ￥{{ sku.goodsPrice | unitPrice }}
                  <text class="price_num">x {{ sku.num }}</text>
                  <!-- <span v-if="sku.point">+{{ sku.point }}积分</span> -->
                  <!-- <text
                    style="font-size: 24rpx; margin-left: 14rpx; color: #ff9900"
                    v-if="sku.isRefund && sku.isRefund !== 'NO_REFUND'"
                  >
                    {{ refundPriceList(sku.isRefund) }} ({{
                      sku.refundPrice | unitPrice("￥")
                    }})
                  </text> -->
                </view>
                <!-- 申请售后 -->
                <view
                  class="apply-view"
                  v-if="
                   ( order.orderStatus == 'DELIVERED' ||
                    order.orderStatus == 'COMPLETED' ||
                    order.orderStatus == 'UNDELIVERED'|| order.orderStatus=='DELIVERING' ) &&
                   sku.afterSaleStatus != 'ALREADY_APPLIED'
                  "
                  @click.stop="applyService(sku.sn, order, sku)"
                  >申请售后 </view
                >
              </view>
              <!-- <view class="goods-num">
                <view>x{{ sku.num }}</view>

                <view class="good-complaint">
                  <u-tag
                    size="mini"
                    mode="plain"
                    @click="complaint(sku)"
                    v-if="sku.complainStatus == 'NO_APPLY'"
                    text="投诉"
                    type="info"
                  />
                </view>
              </view> -->
            </view>
          </view>
        </view>
        <view class="Total">
          <text>共1件</text>
          <view class="total" v-if="order.priceDetailDTO">
            <text>合计:</text> ￥{{ order.priceDetailDTO.goodsPrice || 0  }}
          </view >
        </view>
      </view>
    </view>
    <view class="info-view info_view_box">
      <view style="width: 100%">
        <!-- 实付金额，点击可展开/收起 -->
        <view
          class="order-info-view order_info_box"
          @click="showDetail = !showDetail"
          style="cursor: pointer; align-items: center"
        >
          <view class="title">实付金额</view>
          <view class="value priceValue" v-if="order.priceDetailDTO">
            ￥{{ order.priceDetailDTO.flowPrice  }}
            <u-icon
              :name="showDetail ? 'arrow-up' : 'arrow-down'"
              size="18"
              style="margin-left: 8rpx"
            />
          </view>
        </view>
        <!-- 展开明细 -->
        <view
          v-if="showDetail"
          style="height: 1px; background: #eee; margin: 0 0 10rpx 0"
        ></view>
        <view v-if="showDetail">
          <view class="order-info-view">
            <view class="title">商品总价：</view>
            <view class="value">￥{{ order.goodsPrice | unitPrice }}</view>
          </view>
          <view class="order-info-view">
            <view class="title">运费：</view>
            <view class="value">￥{{ order.freightPrice | unitPrice }}</view>
          </view>
          <view class="order-info-view" v-if="order.freightPrice">
            <view class="title">运费：</view>
            <view class="value">￥{{ order.freightPrice | unitPrice }}</view>
          </view>
          <view class="order-info-view" v-if="order.priceDetailDTO">
            <view class="title">优惠券：</view>
            <view class="value"
              >￥{{ order.priceDetailDTO.couponPrice | unitPrice }}</view
            >
          </view>
        </view>
      </view>
    </view>
    <!-- 客户服务， 售后，取消订单，查看物流，投诉等 -->
    
    <!-- <view class="info-view">
      <view style="width: 100%">
        <view class="order-info-view">
          <view class="title">服务</view>
        </view>
        <view class="customer-list">
          <view
            class="customer-service"
            v-if="
              orderDetail.allowOperationVO &&
              orderDetail.allowOperationVO.cancel == true
            "
            @click="onCancel(order.sn)"
            >取消订单</view
          >
          <view
            class="customer-service"
            v-if="order.orderStatus == 'DELIVERED'"
            @click="onLogistics(order)"
            >查看物流</view
          >
          <view
            class="customer-service"
            v-if="
              order.orderStatus != 'UNPAID' &&
              order.orderPromotionType == 'PINTUAN'
            "
            @click="ByUserMessage(order)"
            >查看拼团信息</view
          >
          <view class="customer-service" @click="contact(order.storeId)"
            >联系客服</view
          >
        </view>
      </view>
    </view> -->
    <view class="info-view after_sales_view">
      <view style="width: 100%">
        <view class="order-info-view">
          <view class="">订单编号：</view>
          <view class="value" style="width: 80%; display: flex; align-items: center;justify-content: space-between;">
            {{ order.sn }}
            <u-tag
              class="copy"
              text="复制"
              type="info"
              size="mini"
              @click="onCopy(order.sn)"
            />
          </view>
        </view>
        <view class="order-info-view">
          <view class="">下单时间：</view>
          <view class="value">{{ order.createTime }}</view>
        </view>
        <view class="order-info-view">
          <view class="">订单备注：</view>
          <view class="value">{{ order.remark || "暂无备注" }}</view>
        </view>
        <view class="order-info-view">
          <view class="">支付状态：</view>
          <view class="value">
            {{
              order.payStatus == "UNPAID"
                ? "未付款"
                : order.payStatus == "PAID"
                ? "已付款"
                : ""
            }}</view
          >
        </view>
        <!-- <view class="order-info-view">
          <view class="">支付方式：</view>
          <view class="value">{{
            orderDetail.paymentMethodValue || "暂无"
          }}</view>
        </view> -->
      </view>
      <view class="customer_box" @click="contact(order,orderGoodsList)">联系客服</view>
    </view>

    <!-- 支付方式 -->
    <view class="mode_view">
      <view class="title">支付方式</view>
      <view class="value">{{ orderDetail.paymentMethodValue || "暂无" }}</view>
    </view>
   
    <!-- <view class="info-view" v-if="order.payStatus == 'PAID'">
      <view>
        <view class="invoice-info-view">
          <view class="invoice-title">发票信息：</view>
          <view v-if="!order.needReceipt" class="value">无需发票</view>
          <view v-else class="value" @click="onReceipt(orderDetail.receipt)"
            >查看发票</view
          >
        </view>
      </view>
    </view> -->
    <view style="padding-bottom: 150rpx"></view>

    <view class="bottom_view">
      <view class="btn-view u-flex u-row-between">
        <view class="description">
          <!-- 全部 -->
          <!-- 等待付款 -->

          <text v-if="order.payStatus === 'PAID'">已付金额：</text>
          <text v-else>应付金额：</text>

          <text class="price" v-if="order.priceDetailDTO"
            >￥{{ order.priceDetailDTO.flowPrice  }}</text
          >
        </view>
        <view>
          <!-- 全部 -->
          <!-- 等待付款 -->
          <u-button
            type="error"
            shape="circle"
            size="medium"
            :custom-style="customStyle"
            v-if="
              orderDetail.allowOperationVO && orderDetail.allowOperationVO.pay
            "
            @click="toPay(order)"
            >立即付款</u-button
          >

          <!-- <u-button class="rebuy-btn" size="mini" v-if="order.order_operate_allowable_vo.allow_service_cancel"> 提醒发货</u-button> -->
          <!-- <div class="pay-btn">确认收货</div> -->
          <u-button
            shape="circle"
            ripple
            type="warning"
            size="medium"
            v-if="order.orderStatus == 'DELIVERED'"
            @click="onRog(order.sn)"
            >确认收货</u-button
          >
          <!-- 交易完成 未评价 -->
          <u-button
            shape="circle"
            ripple
            size="medium"
            v-if="order.orderStatus == 'COMPLETE'"
            @click="onComment(order.sn)"
            >评价商品</u-button
          >
          <u-button
            type="primary"
            shape="circle"
            size="medium"
            :custom-style="customStyle"
            v-if="order.orderStatus === 'CANCELLED' || order.orderStatus === 'PAY_FAIL'"
            @click="rebuyNow(orderGoodsList)"
          >
            再次购买
          </u-button>
          <u-button
            type="primary"
            shape="circle"
            size="medium"
            :custom-style="customStyle"
            v-if="order.orderStatus === 'DELIVERING'"
            @click="goHome()"
          >
            逛逛其他商品
          </u-button>
        </view>
      </view>
     
    </view>
    
    <u-popup
      class="cancel-popup"
      v-model="cancelShow"
      mode="bottom"
      length="60%"
    >
      <view class="header">取消订单</view>
      <view class="body">
        <view class="title"
          >取消订单后，本单享有的优惠可能会一并取消，是否继续？</view
        >
        <view>
          <u-radio-group v-model="reason">
            <view class="value">
              <view
                class="radio-view"
                v-for="(item, index) in cancelList"
                :key="index"
              >
                <u-radio
                  :active-color="lightColor"
                  label-size="25"
                  shape="circle"
                  :name="item.reason"
                  @change="reasonChange"
                  >{{ item.reason }}</u-radio
                >
              </view>
            </view>
          </u-radio-group>
        </view>
      </view>
      <view class="footer">
        <u-button
          size="medium"
          v-if="reason"
          shape="circle"
          @click="submitCancel"
          >提交</u-button
        >
      </view>
    </u-popup>
    <u-toast ref="uToast" />
    <u-modal
      v-model="rogShow"
      :show-cancel-button="true"
      :content="'是否确认收货?'"
      :confirm-color="lightColor"
      @confirm="confirmRog"
    ></u-modal>

    <!-- 分享 -->
    <shares
      v-if="shareFlag"
      :thumbnail="orderDetail.orderItems[0].image"
      :goodsName="orderDetail.orderItems[0].goodsName"
      @close="shareFlag = false"
    />
  </view>
</template>

<script>
import { getExpress, getPackage } from "@/api/trade.js";
import { cancelOrder, confirmReceipt, getOrderDetail } from "@/api/order.js";
import * as API_trade from "@/api/trade.js"; // 引入购物车相关API
import * as API_Trade from "@/api/trade";
import shares from "@/components/m-share/index"; //分享

import { getClearReason } from "@/api/after-sale.js";

export default {
  components: {
    shares,
  },
  data() {
    return {
      lightColor: this.$lightColor,
      logisticsList: { traces: [] }, //物流信息
      shareFlag: false, //拼团分享开关
      orderStatusList: {
        UNPAID: {
          title: "未付款",
          value: "商品暂未付款",
        },
        PAID: {
          title: "已付款",
          value: "买家已付款",
        },
        UNDELIVERED: {
          title: "待发货",
          value: "商品等待发货中",
        },
        PARTS_DELIVERED: {
          title: "部分发货",
          value: "商品已部分发货。",
        },
        DELIVERED: {
          title: "已发货",
          value: "商品已发货,请您耐心等待",
        },
        DELIVERING: {
          title: "发货中",
          value: "商品已发货，请耐心等待",
        },

        CANCELLED: {
          title: "已取消",
          value: "订单已取消",
        },
        COMPLETED: {
          title: "已完成",
          value: "订单已完成,祝您生活愉快",
        },
        STAY_PICKED_UP: {
          title: "待自提",
          value: "商品正在等待提取",
        },
        TAKE: {
          title: "待核验",
        },
        PAY_FAIL:{
          title: "支付失败",
          value: "订单支付失败",
        },
        PAY_DOING:{
          title: "支付中",
          value: "订单正在支付中",
        }
      },
      order: {},
      cancelShow: false, //取消订单
      orderSn: "",
      orderGoodsList: "", //订单中商品集合
      orderDetail: "", //订单详情信息
      sn: "",
      cancelList: "",
      rogShow: false,
      reason: "",
      orderPackage: "",
      showDetail: false,
      customStyle: {
        background: " linear-gradient( 90deg, #FF5235 0%, #FF9500 100%)",
      },
      timestamp: 86400,
      logisticsInfo: {
        status: "已收货",
        branch: "如东县城中集散点",
        address: "北京市朝阳区建国门外大街6号院",
        consignee: "张三",
        mobile: "1898****6632",
        protect: true,
      },
    };
  },
  onLoad(options) {
    this.loadData(options.sn);
    this.sn = options.sn;
  },
  methods: {
    //获取包裹
    async getOrderPackage() {
      getPackage(this.order.sn).then((res) => {
        if (res.data.success) {
          this.orderPackage = res.data.result;
        }
      });
    },
    timeFn(val) {
      return (val - new Date().getTime()) / 1000;
    },
    handleClickDeliver() {
      uni.navigateTo({
        url: `/pages/order/deliverDetail?order_sn=${this.order.sn}`,
      });
    },
    // 退款状态枚举
    refundPriceList(status) {
      switch (status) {
        case "ALL_REFUND":
          return "全部退款";
        case "PART_REFUND":
          return "部分退款";
        case "NO_REFUND":
          return "未退款";
        case "REFUNDING":
          return "退款中";
        default:
          return "";
      }
    },
    callPhone() {
      this.$options.filters.callPhone(this.order.storeAddressMobile);
    },
    //联系客服
    contact(order,sku) {
      // this.$options.filters.talkIm(storeId);
      this.$options.filters.talkIm(
        order.storeId,
        sku[0].goodsId,
        sku[0].skuId,
        order.sn  // 传递订单号
      );
    },
    goToShopPage(val) {
      uni.navigateTo({
        url: "/pages/product/shopPage?id=" + val.storeId,
      });
    },
    // 获取物流信息
    loadLogistics(sn) {
      getExpress(sn).then((res) => {
        // 兜底处理，保证logisticsList至少是对象且有traces数组
        this.logisticsList = res.data.result || { traces: [] };
        console.log(this.logisticsList);
      });
    },

    // 分享当前拼团信息
    inviteGroup() {
      this.shareFlag = true;
    },
    // #TODO 这块需要写一下 目前没有拼团的详细信息
    ByUserMessage(order) {
      uni.navigateTo({
        url:
          "/pages/cart/payment/shareOrderGoods?sn=" +
          order.sn +
          "&sku=" +
          this.orderGoodsList[0].skuId +
          "&goodsId=" +
          this.orderGoodsList[0].goodsId,
      });
    },
    async loadData(sn) {
      // uni.showLoading({
      //   title: "加载中",
      // });
      getOrderDetail(sn).then((res) => {
        const order = res.data.result;
        this.order = order.order;
        this.orderGoodsList = order.orderItems;
        this.orderDetail = res.data.result;
        if (this.order.deliveryMethod === "LOGISTICS") {
          this.loadLogistics(sn);
          // this.getOrderPackage();
        }
        if (this.$store.state.isShowToast) {
          // uni.hideLoading();
        }
      });
    },
    onReceipt(val) {
      uni.navigateTo({
        url: "/pages/order/invoice/invoiceDetail?id=" + val.id,
      });
    },
    gotoGoodsDetail(sku) {
      uni.navigateTo({
        url: `/pages/product/goods?id=${sku.skuId}&goodsId=${sku.goodsId}`,
      });
    },
    onCopy(sn) {
      this.$options.filters.setClipboard(sn);
    },

    /**
     * 投诉
     */
    complaint(sku) {
      uni.navigateTo({
        url:
          "/pages/order/complain/complain?sn=" +
          this.sn +
          "&skuId=" +
          sku.skuId,
      });
    },
    //售后按钮
    onAfterSales(sn, sku) {
      uni.navigateTo({
        url: `./afterSales/afterSalesSelect?sn=${sn}&sku=${encodeURIComponent(
          JSON.stringify(sku)
        )}`,
      });
    },
    // 去支付
    toPay(val) {
      val.sn
        ? uni.navigateTo({
            url: "/pages/cart/payment/payOrder?order_sn=" + val.sn,
          })
        : false;
    }, //删除订单
    deleteOrder(index) {
      uni.showLoading({
        title: "请稍后",
      });
      setTimeout(() => {
        this.navList[this.tabCurrentIndex].orderList.splice(index, 1);
        if (this.$store.state.isShowToast) {
          uni.hideLoading();
        }
      }, 600);
    },
    //取消订单
    onCancel(sn) {
      this.orderSn = sn;

      uni.showLoading({
        title: "加载中",
      });
      getClearReason().then((res) => {
        if (res.data.result.length >= 1) {
          this.cancelList = res.data.result;
        }
        if (this.$store.state.isShowToast) {
          uni.hideLoading();
        }
      });

      this.cancelShow = true;
    },

    //提交取消订单（未付款）
    submitCancel() {
      cancelOrder(this.orderSn, { reason: this.reason }).then((res) => {
        if (res.data.success) {
          uni.showToast({
            title: "已取消",
            duration: 2000,
            icon: "none",
          });
          this.cancelShow = false;
          setTimeout(() => {
            uni.reLaunch({
              url: "/pages/order/myOrder?status=0",
            });
          }, 500);
        } else {
          uni.showToast({
            title: res.data.message,
            duration: 2000,
            icon: "none",
          });
          this.cancelShow = false;
        }
      });
    },

    //确认收货
    onRog(sn) {
      console.log("确认收货", sn);
      
      this.orderSn = sn;
      this.rogShow = true;
    },
    confirmRog() {
      confirmReceipt(this.orderSn).then((res) => {
        if (res.data.success) {
          uni.showToast({
            title: "已确认收货",
            duration: 2000,
            icon: "none",
          });
          this.rogShow = false;
          this.loadData(this.sn);
        }
      });
    },
    //评价商品
    onComment(sn) {
      uni.navigateTo({
        url: "./evaluate/myEvaluate",
      });
    }, //查看物流
    onLogistics(order) {
      uni.navigateTo({
        url:
          "/pages/mine/msgTips/packageMsg/logisticsDetail?logi_id=" +
          order.logi_id +
          "&ship_no=" +
          order.ship_no +
          "&order_sn=" +
          order.sn,
      });
    },

    //选择取消原因
    reasonChange(reason) {
      this.reason = reason;
    },
    reBuy(order) {
      uni.navigateTo({
        url:
          "/pages/product/goods?id=" + order.id + "&goodsId=" + order.goodsId,
      });
    },
    async buyAgain(orderDetail) {
      // 假设orderDetail.items是商品数组
      console.log(orderDetail);
      // 调用添加购物车接口
      const res = await API_trade.addToCart({
        skuId: orderDetail.id,
        num: 1,
      });
      return;
      try {
        for (const item of orderDetail.items) {
        }
        uni.showToast({ title: "已加入购物车", icon: "success" });
        // 可选：跳转到购物车页面
        // uni.switchTab({ url: '/pages/cart/cart' });
      } catch (e) {
        uni.showToast({ title: "加入购物车失败", icon: "none" });
      }
    },

    formatSpecs(specs) {
      if (!specs) return "无规格";
      // 如果是字符串，尝试解析
      if (typeof specs === "string") {
        try {
          specs = JSON.parse(specs);
        } catch (e) {
          return "无规格";
        }
      }
      if (typeof specs !== "object") return "无规格";
      const arr = Object.keys(specs)
        .filter(
          (key) =>
            key !== "images" &&
            specs[key] !== undefined &&
            specs[key] !== null &&
            specs[key] !== ""
        )
        .map((key) => `${key}: ${specs[key]}`);
      return arr.length ? arr.join("；") : "无规格";
    },
    // rebuyNow(goodsList) {
    //   // 只传递必要字段，避免URL过长
    //   const buyList = goodsList.map(item => ({
    //     skuId: item.skuId,
    //     goodsId: item.goodsId,
    //     num: item.num,
    //     specs: item.specs // 如需传递规格详情
    //   }));
    //   // 建议用 encodeURIComponent 防止特殊字符出错
    //   const buyListStr = encodeURIComponent(JSON.stringify(buyList));
    //   uni.navigateTo({
    //     url: `/pages/order/fillorder?buyList=${buyListStr}`
    //   });
    // },
    rebuyNow(goodsList) {
      console.log(goodsList, "BUY_NOW");

      // 构建正确的数据结构
      let data = {
        skuId: goodsList[0].skuId,
        num: goodsList[0].num,
        cartType: "BUY_NOW", // 明确指定购买类型
      };
      API_Trade.addToCart(data).then((res) => {
        if (res.data.success) {
          uni.navigateTo({
            url: `/pages/order/fillorder?way=${
              data.cartType
            }&addr=${""}&parentOrder=${encodeURIComponent(JSON.stringify(""))}`,
          });
        }
      });
    },
    goHome() {
      uni.reLaunch({ url: "/pages/tabbar/home/<USER>" });
    },
    /**
     * 申请售后
     */
    applyService(sn, order, sku) {
      console.log(sn, order.orderStatus);
      // 判断发货状态
      if ( order.orderStatus === "DELIVERING" || order.orderStatus === "UNDELIVERED") {
        // 未发货
        // 这里可以做你想做的逻辑，比如弹窗提示、跳转不同页面等
        uni.showToast({
          title: "该商品未发货",
          icon: "none",
        });
        // return; // 如果不想继续后续逻辑可以加 return
        uni.navigateTo({
          url: `/pages/order/afterSales/afterSalesDetail?sn=${sn}&value=${3}&flowPrice=${order.flowPrice}`,
        });
      } else if (
        order.orderStatus === "DELIVERED" ||
        order.orderStatus === "PARTS_DELIVERED" ||
       
        order.orderStatus === "COMPLETED"
      ) {
        console.log("已发货");

        // 已发货
        // 这里可以做你想做的逻辑，比如弹窗提示、跳转不同页面等
        uni.showToast({
          title: "该商品已发货",
          icon: "none",
        });
        // return; // 如果不想继续后续逻辑可以加 return
        uni.navigateTo({
          url: `/pages/order/afterSales/afterSalesDetail?sn=${sn}&value=${1}&flowPrice=${order.flowPrice}`,
        });
      }
    },
    gotoLogisticsDetail() {
      // 跳转到物流详情页
      uni.navigateTo({
        url: "/pages/order/deliverDetail?order_sn=" + this.sn,
      });
    },
  },
  computed: {
    lastAcceptStation() {
      // 兜底处理，保证traces为数组
      const traces = (this.logisticsList && this.logisticsList.traces) || [];
      if (traces.length > 0) {
        return traces[traces.length - 1].AcceptStation || '网点信息';
      }
      return '网点信息';
    }
  }
};
</script>

<style lang="scss">
@import "./goods.scss";

.empty {
  width: 100%;
}

.customer-service {
  background: #ededed;
  // padding: 12rpx 40rpx;
  width: 48%;
  margin: 0 1%;
  height: 55rpx;
  line-height: 55rpx;
  margin-bottom: 10rpx;
  text-align: center;
  font-size: 24rpx;
  border-radius: 10rpx;
}

.customer-list {
  display: flex;
  flex-wrap: wrap;
}

.logistics-view {
  justify-content: space-between;
  padding: 30rpx !important;
  margin: 0 !important;
  transform: translateY(-10px);
}

.order-status {
  color: #fff;
  width: 100%;
  text-align: left;
  font-size: 36rpx;
  margin-top: 40rpx;
  // margin-bottom: 40rpx;
  > div {
    font-size: 24rpx;
    margin-top: 10rpx;
  }
  text {
    margin-left: 64rpx;
  }
}

.logistics-List-title {
  margin-bottom: 10rpx;
  font-size: 26rpx;
}

.logistics-List-time {
  font-size: 24rpx;
  color: #999;
}

.info-detail {
  margin-right: 30rpx;
  color: #333;
}

.order-view {
  margin: 0 !important;
  border-radius: 0 !important;
  padding: 0 !important;
  width: 750rpx;
  height: 248rpx;
  background: linear-gradient(
    180deg,
    #ff6634 0%,
    rgba(255, 102, 52, 0) 100%
  ) !important;
}

.Countdown {
  width: 372rpx;
  height: 44rpx;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 90rpx;
  font-weight: 400;
  font-size: 24rpx;
  color: #333333;
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  line-height: 44rpx;
}

page,
.content {
  background: #f1f1f1;
  height: 100%;
}

.info-line {
  align-items: center;
  display: flex;
  border-radius: 30rpx;
  flex-direction: row;
  justify-content: space-between;
  background-color: #fff;
  width: 100%;
  height: 110rpx;
  color: #333333;
  font-size: 28rpx;
  border-bottom: 1rpx solid #eeeeee;

  .info-title {
    margin: 0 30rpx;
    padding: 16rpx 0rpx;
  }
}

.seller-view {
  margin: 20rpx 0;
  padding: 32rpx;
  border-radius: 30rpx;
}

.address-title {
  font-size: 26rpx;
  font-weight: bold;

  > span {
    margin-right: 20rpx;
  }
}
.address_title {
  width: 100%;
  display: flex;
  justify-content: space-between;
  color: #333;
  font-weight: normal;
  font-size: 32rpx;
}

.info-view {
  display: flex;
  margin: 0 0 20rpx 0;
  border-radius: 30rpx;
  flex-direction: row;
  padding: 15rpx 30rpx;
  margin-bottom: 20rpx;
  background-color: #fff;

  .address-view {
    width: 100%;
    // display: flex;
    // flex-direction: row;
    padding: 16rpx 0;

    .address {
      color: $font-color-light;
      overflow: hidden;
      line-height: 1.75;
      font-size: 22rpx;
    }
  }

  .address_box {
    margin: 40rpx auto !important;
    width: 686rpx;
    // height: 168rpx;
    background: #ffffff;
    border-radius: 20rpx 20rpx 20rpx 20rpx;
  }
  .address_boxText {
    display: flex;
    align-items: center;
    margin-top: 20rpx;
    font-weight: 400;
    font-size: 28rpx !important;
    color: #333333 !important;
    justify-content: space-around;
  }
  .address_boxText_text_cent {
    //2行显示超出...
    width: 90%;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    line-height: 1.2;
    font-size: 28rpx !important;
  }
  .order-info-view {
    line-height: 60rpx;
    display: flex;
    flex-direction: row;
    width: 100%;
    // margin: 10rpx 0rpx;

    .title {
      color: #666;
      width: 140rpx;
      font-size: 24rpx;
      font-weight: 600;
      flex: 3;
      min-width: 160rpx;
    }

    .value {
      color: #666;
      font-size: 24rpx;
      // flex: 10;
    }

    .copy {
      // margin-left: 56rpx;
      width: 68rpx;
      height: 40rpx;
      border-radius: 62rpx;
      border: 1rpx solid rgba(0, 0, 0, 0.1);
      font-weight: 400;
      font-size: 16rpx;
      color: #999999;
      text-align: center;
      line-height: 24rpx;
      background: #fff;
    }
  }

  .invoice-info-view {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    width: 100%;
    margin: 10rpx 0rpx;

    .invoice-title {
      width: 550rpx;
      font-size: 28rpx;
      color: #333333;
    }

    .value {
      color: $font-color-light;
    }
  }
}

.verificationCode {
  font-weight: bold;
  letter-spacing: 2rpx;
}

.bottom_view {
  width: 100%;
  height: 100rpx;
  background-color: #ffffff;
  position: fixed;
  bottom: 0;
  left: 0;

  .btn-view {
    padding: 0 30rpx;
    line-height: 100rpx;
    font-size: 26rpx;

    .description {
      font-weight: 400;
      font-size: 24rpx;
      color: #333333;
      .price {
        font-weight: 500;
        font-size: 36rpx;
        color: #ff5134;
      }
    }
  }

  .cancel-btn {
    color: #999999;
    border-color: #999999;
    margin-left: 15rpx;
    height: 60rpx;
  }
}

.cancel-popup {
  .header {
    display: flex;
    flex-direction: row;
    justify-content: center;
    margin: 15rpx 0rpx;
  }

  .body {
    padding: 30rpx;

    .title {
      font-weight: 600;
    }

    .value {
      display: flex;
      flex-direction: column;

      .radio-view {
        margin: 10rpx 0rpx;
      }
    }
  }

  .footer {
    text-align: center;
  }
}
.address-line-height {
  line-height: 1.75;
}
.seller-name {
  > .name {
    flex: 10 !important;
  }
  > .status {
    flex: 2;
  }
}
.shop-info-view {
  width: 686rpx;
  margin: 50rpx auto 0;
  .name_text {
    padding-bottom: 32rpx;
    // padding: 32rpx 0 32rpx 0;
    border-bottom: 1rpx solid #f5f5f5;
  }
}
.goods_info {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  // height: 148rpx;
  position: relative;
}
.goods_specs {
  font-weight: 400;
  font-size: 24rpx;
  color: #999999;
}
.apply-view {
  font-weight: 400;
  font-size: 24rpx;
  color: #ff5134;
  position: absolute;
  right: 32rpx;
  bottom: 12rpx;
}
.Total {
  margin-top: 32rpx;
  display: flex;
  justify-content: flex-end;
  font-weight: 400;
  font-size: 28rpx;
  color: #333333;
  .total {
    margin-left: 10rpx;
    color: #ff5134;
    text {
      color: #333;
    }
  }
}
.info_view_box {
  width: 686rpx;
  background: #ffffff;
  border-radius: 20rpx;
  margin: 0 auto;
  .order_info_box {
    width: 100%;
  }
  .order-info-view {
    .title {
      font-weight: 400;
      font-size: 24rpx;
      color: #999999;
    }
    .value {
      font-weight: 400;
      font-size: 24rpx;
      color: #333333;
    }
    .priceValue {
      font-weight: 400;
      font-size: 28rpx;
      color: #333333;
      margin-bottom: 10rpx;
    }
  }
}
.after_sales_view {
  width: 686rpx;
  margin: 20rpx auto;
  color: #999999;
  font-weight: 400;
  font-size: 24rpx;
  position: relative;
  padding: 20rpx 32rpx;
  .value {
    color: #999999 !important;
    font-weight: 400;
    font-size: 24rpx;
  }
  .customer_box {
    position: absolute;
    width: 140rpx;
    height: 60rpx;
    border-radius: 190rpx 190rpx 190rpx 190rpx;
    border: 2rpx solid rgba(0,0,0,0.1);
    bottom: 30rpx;
    right: 30rpx;
    text-align: center;
    line-height: 56rpx;
  }
}
.mode_view {
  width: 686rpx;
  height: 104rpx;
  background: #ffffff;
  border-radius: 20rpx;
  margin: 20rpx auto;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 32rpx;
  box-sizing: border-box;
  font-weight: 400;
  font-size: 24rpx;
  color: #333333;
}

.logistics-card {
  width: 686rpx;
  height: 264rpx;
  margin: 40rpx auto;
  padding: 32rpx;
  background: #fff;
  border-radius: 20rpx;
  .logistics-header {
    .logistics-box {
      display: flex;
      justify-content: space-between;
      .timeline_right {
        font-weight: 400;
        font-size: 28rpx;
        color: #999999;
        text {
          margin: 0;
        }
      }
    }
    
    margin-bottom: 20rpx;
    .image {
      width: 48rpx;
      height: 48rpx;
      margin-right: 16rpx;
    }
    .text {
      flex: 1;
      font-weight: bold;
      font-size: 30rpx;
    }
    .detail {
      margin-left: auto;
      text-align: right;
      .text {
        color: #ff5235;
        font-size: 26rpx;
        font-weight: 500;
        cursor: pointer;
      }
    }
    .timeline_right_lastAcceptStation {
      width: 560rpx;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
  .logistics-address {
   
    .address_order {
      width: 560rpx;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .address {
      color: #ff5235;
      font-size: 28rpx;
      margin-bottom: 8rpx;
    }
    .consignee {
      color: #666;
      font-size: 24rpx;
    }
    .mobile {
      color: #666;
      font-size: 24rpx;
    }
    .protect {
      color: #999;
      font-size: 24rpx;
    }
    .protect_bg {
      width: 110rpx;
      height: 28rpx;
      background: #F3F3F3;
      border-radius: 4rpx;
      padding: 0 6rpx;
      text-align: center;
      line-height: 28rpx;
      margin-left: 12rpx;
    }
  }
}

.logistics-timeline {
  display: flex;
  align-items: flex-start;
  position: relative;
  .timeline-icon {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-right: 20rpx;
    position: relative;
    width: 48rpx;
    .timeline-logo {
      width: 40rpx;
      height: 40rpx;
      border-radius: 50%;
      background: #fff;
      z-index: 2;
    }
    .timeline-line {
      width: 2rpx;
      background: #ffb6a7;
      flex: 1;
      min-height: 92rpx;
      margin-top: 4rpx;
      margin-bottom: 0;
      z-index: 1;
    }
    .timeline-dot {
      width: 16rpx;
      height: 16rpx;
      border-radius:50%;
      border: 2rpx solid #FFCFC5;
    }
  }
  .timeline-content {
    flex: 1;
  }
}
/deep/ .u-size-medium {
  width: 196rpx;
  height: 80rpx;
}
</style>
